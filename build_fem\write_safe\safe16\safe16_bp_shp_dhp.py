"""
SAFE16 Pile Data Writer Module

Converts pile geometry, properties, and spring data from Excel inputs to SAFE16 format
for bored piles (BP), sheet piles (SHP), and driven hollow piles (DHP).
"""

from typing import Any, Dict, Union, List
import pandas as pd
import pandas as pd


def _write_pile_points_safe16(excel_inputs: Any, safe16_dfs: Any) -> Any:
    """
    Write pile point coordinates to SAFE16 Object Geometry Point Coordinates table.
    Extracts coordinates from LateralSoilSpring sheet and marks all points as special.
    """    
    # Extract pile point data from lateral soil spring spreadsheet
    df_soil_spring = excel_inputs.LateralSoilSpring.copy()

    # Create SAFE16 point coordinate data structure
    # Uses multi-level column headers specific to SAFE16 format
    data_safe16 = {
        ('TABLE:  Object Geometry - Point Coordinates', 'Point', 'Text'): df_soil_spring['Point Name'].values,
        ('TABLE:  Object Geometry - Point Coordinates', 'GlobalX', 'm'): df_soil_spring['X (m)'].values,
        ('TABLE:  Object Geometry - Point Coordinates', 'GlobalY', 'm'): df_soil_spring['Y (m)'].values,
        ('TABLE:  Object Geometry - Point Coordinates', 'GlobalZ', 'm'): df_soil_spring['Z (m)'].values,
        ('TABLE:  Object Geometry - Point Coordinates', 'SpecialPt', 'Yes/No'): 'Yes'    }
    
    # Convert to DataFrame and append to existing point coordinates
    df_append_safe16 = pd.DataFrame(data_safe16)
    safe16_dfs.ObjGeomPointCoordinates = pd.concat(
        [safe16_dfs.ObjGeomPointCoordinates, df_append_safe16],
        ignore_index=True
    )
    return safe16_dfs


def _write_pile_column_safe16(excel_inputs: Any, safe16_dfs: Any) -> Any:
    """
    Write pile column line segments to SAFE16 Object Geometry Lines table.
    Creates line segments connecting pile points using pile naming conventions.
    """    # Extract all pile point names from lateral soil spring data
    all_pile_point_names = excel_inputs.LateralSoilSpring['Point Name'].copy()    # Determine segment names: points not immediately followed by '_B' points
    # This identifies the start of each line segment for pile columns
    mask_segment_names = ~(
        all_pile_point_names.str.contains('_B', na=False).shift(-1, axis=0).fillna(False).astype(bool))
    segment_names = all_pile_point_names[mask_segment_names].reset_index(drop=True)

    # Segment start points: all points except those ending with '_T'
    mask_segment_starts = ~all_pile_point_names.str.contains('_T', na=False)
    segment_starts = all_pile_point_names[mask_segment_starts].reset_index(drop=True)

    # Segment end points: all points except those ending with '_B'  
    mask_segment_ends = ~all_pile_point_names.str.contains('_B', na=False)
    segment_ends = all_pile_point_names[mask_segment_ends].reset_index(drop=True)

    # Create SAFE16 line geometry data with multi-level column headers
    data_safe16 = {
        ('TABLE:  Object Geometry - Lines 01 - General', 'Line', 'Text'): segment_names.values,
        ('TABLE:  Object Geometry - Lines 01 - General', 'PointI', 'Text'): segment_starts.values,
        ('TABLE:  Object Geometry - Lines 01 - General', 'PointJ', 'Text'): segment_ends.values,
        ('TABLE:  Object Geometry - Lines 01 - General', 'LineType', 'Text'): None,
        ('TABLE:  Object Geometry - Lines 01 - General', 'Length', 'm'): None    
        }
    
    # Convert to DataFrame and append to existing line geometry
    df_append_safe16 = pd.DataFrame(data_safe16)
    safe16_dfs.ObjGeomLines01General = pd.concat(
        [safe16_dfs.ObjGeomLines01General, df_append_safe16], ignore_index=True
    )
    return safe16_dfs


def _write_pile_prop_assign_safe16(excel_inputs: Any, safe16_dfs: Any) -> Any:
    """
    Assign pile section properties to pile column lines in SAFE16.
    Maps pile section properties to line segments based on pile identifiers.
    """
    df_pile = excel_inputs.Pile.copy()
    df_soil_spring = excel_inputs.LateralSoilSpring.copy()

    # Filter out points that come immediately before '_B' points
    # This ensures we only process the primary segment identifiers
    mask_before_B = df_soil_spring['Point Name'].str.endswith('_B').shift(-1).fillna(False).astype(bool)
    target_identifiers = df_soil_spring.loc[~mask_before_B, 'Point Name']    # Extract base pile identifiers for property lookup (e.g., 'P1_T' -> 'P1')
    base_pile_ids = target_identifiers.str.split('_').str[0]

    # Create efficient mapping from pile marks to their section properties
    pile_section_map = df_pile.set_index('Pile Mark')['Pile Section']

    # Map base pile IDs to their corresponding section properties
    mapped_pile_sections = base_pile_ids.map(pile_section_map)

    # Create SAFE16 column property assignment data
    data_safe16 = {
        ('TABLE:  Column Property Assignments', 'Line', 'Text'): target_identifiers,
        ('TABLE:  Column Property Assignments', 'ColProp', 'Text'): mapped_pile_sections
    }
    df_append_safe16 = pd.DataFrame(data_safe16)
    safe16_dfs.ColumnPropertyAssignments = pd.concat(
        [safe16_dfs.ColumnPropertyAssignments, df_append_safe16], ignore_index=True
    )
    return safe16_dfs


def _write_pile_point_insertion_safe16(excel_inputs: Any, safe16_dfs: Any) -> Any:
    """
    Define pile column insertion points for SAFE16 analysis.
    Sets up insertion point properties for pile elements with centroid positioning.
    """    
    # Extract pile point data and create working copy
    df_soil_spring = excel_inputs.LateralSoilSpring.copy()

    # Filter points to exclude those immediately before '_B' points
    mask_before_B = df_soil_spring['Point Name'].str.endswith('_B').shift(-1).fillna(False).astype(bool)
    pile_points_identifiers = df_soil_spring.loc[~mask_before_B, 'Point Name']

    # Configure SAFE16 column insertion point data with centroid positioning
    data_safe16 = {
        ('TABLE:  Column Insertion Point', 'Line', 'Text'): pile_points_identifiers,
        ('TABLE:  Column Insertion Point', 'CardinalPt', 'Text'): '10 (centroid)',
        ('TABLE:  Column Insertion Point', 'OffsetXI', 'm'): 0,
        ('TABLE:  Column Insertion Point', 'OffsetYI', 'm'): 0,
        ('TABLE:  Column Insertion Point', 'OffsetZI', 'm'): 0,
        ('TABLE:  Column Insertion Point', 'OffsetXJ', 'm'): 0,
        ('TABLE:  Column Insertion Point', 'OffsetYJ', 'm'): 0,
        ('TABLE:  Column Insertion Point', 'OffsetZJ', 'm'): 0
    }
    df_append_safe16 = pd.DataFrame(data_safe16)
    safe16_dfs.ColumnInsertionPoint = pd.concat(
        [safe16_dfs.ColumnInsertionPoint, df_append_safe16], ignore_index=True
    )

    return safe16_dfs


def _write_pile_local_axis_safe16(excel_inputs: Any, safe16_dfs: Any) -> Any:
    """
    Configure pile column local axis orientations for SAFE16 analysis.
    Calculates and assigns local axis rotations based on pile geometry.
    """
    df_pile_properties = excel_inputs.Pile.copy()
    df_soil_spring = excel_inputs.LateralSoilSpring.copy()

    # Identify rows that come before rows ending with '_B' and exclude them
    mask_before_B = df_soil_spring['Point Name'].str.endswith('_B').shift(-1).fillna(False).astype(bool)
    target_identifiers = df_soil_spring.loc[~mask_before_B, 'Point Name']

    # Extract base pile IDs (e.g., 'P1_T' -> 'P1')
    base_pile_ids = target_identifiers.str.split('_').str[0]

    # Prepare a DataFrame for merging, linking target_identifiers with base_pile_ids
    df_for_merge = pd.DataFrame({
        'TargetIdentifier': target_identifiers,
        'BasePileID': base_pile_ids
    })

    # Select necessary columns from df_pile_properties for the merge
    pile_props_to_merge = df_pile_properties[[
        'Pile Mark', 'X (m)', 'Y (m)', 'BX (m)', 'BY (m)', 'Pile Local Axis (Deg)'
    ]]

    # Merge to get pile properties aligned with target_identifiers
    df_merged_properties = pd.merge(
        df_for_merge,
        pile_props_to_merge,
        left_on='BasePileID',
        right_on='Pile Mark',
        how='left'
    )

    # Calculate the condition for adding 90 degrees
    # Ensure that BX (m) and BY (m) might be NaN if not present for a pile, handle appropriately if necessary (e.g. fillna)
    # Assuming they are present as per original logic implied by direct comparison.
    condition_add_90_degrees = (
            (df_merged_properties['X (m)'] != df_merged_properties['BX (m)']) &
            (df_merged_properties['Y (m)'] != df_merged_properties['BY (m)'])
    )

    # Calculate local axis for SAFE16
    local_axis_safe16 = (
            df_merged_properties['Pile Local Axis (Deg)'] + 90 * condition_add_90_degrees
    )

    # SAFE16: Column Local Axes
    data_safe16 = {
        ('TABLE:  Column Local Axes', 'Line', 'Text'): df_merged_properties['TargetIdentifier'],
        # or simply target_identifiers
        ('TABLE:  Column Local Axes', 'Angle', 'Degrees'): local_axis_safe16
    }
    df_append_safe16 = pd.DataFrame(data_safe16)
    safe16_dfs.ColumnLocalAxes = pd.concat(
        [safe16_dfs.ColumnLocalAxes, df_append_safe16], ignore_index=True
    )
    return safe16_dfs


def _write_pile_end_release_safe16(excel_inputs: Any, safe16_dfs: Any) -> Any:
    """
    Configure pile end releases for pinned pile head conditions in SAFE16.
    Applies moment releases at pile tops for pinned head conditions.
    """
    df_pile_properties = excel_inputs.Pile.copy()
    df_soil_spring_details = excel_inputs.LateralSoilSpring.copy()

    # Extract all segment identifiers from pile point data
    all_segment_identifiers = df_soil_spring_details['Point Name'].copy()

    # Extract base pile marks for property lookup (e.g., 'P1' from 'P1_T')
    base_pile_marks = all_segment_identifiers.str.split('_').str[0]

    # Create temporary DataFrame linking segment identifiers to base pile marks
    df_temp = pd.DataFrame({
        'SegmentIdentifier': all_segment_identifiers,
        'BasePileMark': base_pile_marks
    })

    # Merge with pile properties to get head condition information
    df_merged = pd.merge(
        df_temp,
        df_pile_properties[['Pile Mark', 'Pile Head Condition (PIN/FIX)']],
        left_on='BasePileMark',
        right_on='Pile Mark',
        how='left'
    )

    # Filter for segments with pinned heads at top points ('_T')
    condition_is_pinned_top = (
            (df_merged['Pile Head Condition (PIN/FIX)'] == 'PIN') &
            (df_merged['SegmentIdentifier'].str.contains('_T', na=False))
    )

    pinned_top_segments = df_merged.loc[condition_is_pinned_top, 'SegmentIdentifier'].reset_index(drop=True)

    # Skip processing if no pinned segments are found
    if pinned_top_segments.empty:
        return safe16_dfs

    # Configure SAFE16 column end releases for pinned pile heads
    data_safe16 = {
        ('TABLE:  Column End Releases', 'Line', 'Text'): pinned_top_segments,
        ('TABLE:  Column End Releases', 'TI', 'Yes/No'): 'No',
        ('TABLE:  Column End Releases', 'M2I', 'Yes/No'): 'No',
        ('TABLE:  Column End Releases', 'M3I', 'Yes/No'): 'No',
        ('TABLE:  Column End Releases', 'TJ', 'Yes/No'): 'No',
        ('TABLE:  Column End Releases', 'M2J', 'Yes/No'): 'Yes',
        ('TABLE:  Column End Releases', 'M3J', 'Yes/No'): 'Yes'
    }
    df_append_safe16 = pd.DataFrame(data_safe16)
    safe16_dfs.ColumnEndReleases = pd.concat(
        [safe16_dfs.ColumnEndReleases, df_append_safe16], ignore_index=True
    )
    return safe16_dfs


def _add_pile_restraints_safe16(df_pile_subset: pd.DataFrame, safe16_dfs: Any, s16_restraint_values: Dict[str, str]) -> Any:
    """
    Helper function to add pile end restraints to SAFE16 DataFrames.
    Applies restraint conditions to pile bottom points based on configuration.
    """
    if df_pile_subset.empty:
        return safe16_dfs

    # Generate bottom point names by appending '_B' suffix to pile marks
    base_points = df_pile_subset['Pile Mark'] + '_B'

    # Create SAFE16 point restraint assignment data
    data_s16 = {
        ('TABLE:  Point Restraint Assignments', 'Point', 'Text'): base_points,
        ('TABLE:  Point Restraint Assignments', 'Ux', 'Yes/No'): s16_restraint_values['Ux'],
        ('TABLE:  Point Restraint Assignments', 'Uy', 'Yes/No'): s16_restraint_values['Uy'],
        ('TABLE:  Point Restraint Assignments', 'Uz', 'Yes/No'): s16_restraint_values['Uz'],
        ('TABLE:  Point Restraint Assignments', 'Rx', 'Yes/No'): s16_restraint_values['Rx'],
        ('TABLE:  Point Restraint Assignments', 'Ry', 'Yes/No'): s16_restraint_values['Ry'],
        ('TABLE:  Point Restraint Assignments', 'Rz', 'Yes/No'): s16_restraint_values['Rz'],
    }
    df_append_s16 = pd.DataFrame(data_s16)
    safe16_dfs.PointRestraintAssignments = pd.concat(
        [safe16_dfs.PointRestraintAssignments, df_append_s16], ignore_index=True
    )
    return safe16_dfs


def _write_pile_end_restraint_safe16(excel_inputs: Any, safe16_dfs: Any) -> Any:
    """
    Configure pile end restraints based on pile type for SAFE16 analysis.
    Applies different restraint conditions to pile bottom points by pile type.
    """
    df_pile = excel_inputs.Pile.copy()

    # Configuration 1: Standard piles (non-DHP and non-MP)
    # Apply full horizontal and vertical translation restraints
    condition_not_dhp_mp = (df_pile['Pile Type'] != 'DHP') & (df_pile['Pile Type'] != 'MP')
    df_pile_not_dhp_mp = df_pile[condition_not_dhp_mp]

    s16_restraints_not_dhp_mp = {'Ux': 'Yes', 'Uy': 'Yes', 'Uz': 'Yes', 'Rx': 'No', 'Ry': 'No', 'Rz': 'No'}
    safe16_dfs = _add_pile_restraints_safe16(df_pile_not_dhp_mp, safe16_dfs, s16_restraints_not_dhp_mp)

    # Configuration 2: DHP piles (Driven Hollow Piles)
    # Apply vertical restraint only, allowing horizontal movement
    condition_dhp = df_pile['Pile Type'] == 'DHP'
    df_pile_dhp = df_pile[condition_dhp]

    s16_restraints_dhp = {'Ux': 'No', 'Uy': 'No', 'Uz': 'Yes', 'Rx': 'No', 'Ry': 'No', 'Rz': 'No'}
    safe16_dfs = _add_pile_restraints_safe16(df_pile_dhp, safe16_dfs, s16_restraints_dhp)

    return safe16_dfs


def _write_pile_spring_safe16(excel_inputs: Any, safe16_dfs: Any) -> Any:
    """
    Configure pile lateral soil springs for SAFE16 analysis.
    Processes lateral soil spring data and creates spring properties and assignments.
    """
    df_soil_spring = excel_inputs.LateralSoilSpring.copy()

    # Filter for points with valid subgrade reaction values
    condition = ~pd.isna(df_soil_spring['Subgrade Reaction (kN/m3)'])
    df_soil_spring_filtered = df_soil_spring[condition].copy()

    if df_soil_spring_filtered.empty:
        return safe16_dfs

    # Extract spring data for SAFE16 processing
    point_names_filtered = df_soil_spring_filtered['Point Name'].values
    spring_x_filtered = df_soil_spring_filtered['Spring X (kN/m)'].values
    spring_y_filtered = df_soil_spring_filtered['Spring Y (kN/m)'].values

    # Create SAFE16 spring properties data structure
    data_s16_props = {
        ('TABLE:  Spring Properties - Point', 'Spring', 'Text'): point_names_filtered,
        ('TABLE:  Spring Properties - Point', 'Ux', 'kN/m'): spring_x_filtered,
        ('TABLE:  Spring Properties - Point', 'Uy', 'kN/m'): spring_y_filtered,
        ('TABLE:  Spring Properties - Point', 'Uz', 'kN/m'): 0,
        ('TABLE:  Spring Properties - Point', 'Rx', 'kN-m/rad'): 0,
        ('TABLE:  Spring Properties - Point', 'Ry', 'kN-m/rad'): 0,
        ('TABLE:  Spring Properties - Point', 'Rz', 'kN-m/rad'): 0,
        ('TABLE:  Spring Properties - Point', 'NonlinOpt', 'Text'): 'None (Linear)',
        ('TABLE:  Spring Properties - Point', 'Color', 'Text'): 'Green'    }
    df_append_s16_props = pd.DataFrame(data_s16_props)

    # Cast numeric columns to float to ensure proper data types for concatenation
    numeric_cols_to_cast_s16_props = [
        ('TABLE:  Spring Properties - Point', 'Ux', 'kN/m'),
        ('TABLE:  Spring Properties - Point', 'Uy', 'kN/m'),
        ('TABLE:  Spring Properties - Point', 'Uz', 'kN/m'),
        ('TABLE:  Spring Properties - Point', 'Rx', 'kN-m/rad'),
        ('TABLE:  Spring Properties - Point', 'Ry', 'kN-m/rad'),
        ('TABLE:  Spring Properties - Point', 'Rz', 'kN-m/rad')
    ]
    for col_name_tuple in numeric_cols_to_cast_s16_props:
        if col_name_tuple in df_append_s16_props.columns:
            df_append_s16_props[col_name_tuple] = df_append_s16_props[col_name_tuple].astype(float)

    # Handle empty target DataFrame by direct assignment or concatenation
    if safe16_dfs.SpringPropertiesPoint.empty:
        safe16_dfs.SpringPropertiesPoint = df_append_s16_props
    else:
        safe16_dfs.SpringPropertiesPoint = pd.concat(
            [safe16_dfs.SpringPropertiesPoint, df_append_s16_props],
            ignore_index=True
        )

    # Create SAFE16 spring assignment data linking points to spring properties
    data_s16_assign = {
        ('TABLE:  Point Spring Assignments', 'Point', 'Text'): point_names_filtered,
        ('TABLE:  Point Spring Assignments', 'Spring', 'Text'): point_names_filtered
    }
    df_append_s16_assign = pd.DataFrame(data_s16_assign)
    safe16_dfs.PointSpringAssignments = pd.concat(
        [safe16_dfs.PointSpringAssignments, df_append_s16_assign],
        ignore_index=True
    )
    return safe16_dfs


def _write_pile_group_safe16(
    safe16_dfs: Any,
    pile_marks_series: pd.Series,  # Unused but kept for interface consistency
    all_point_names: pd.Series,
    top_points: pd.Series,
    bottom_points: pd.Series,
    line_segments_for_pile_line_group: pd.Series,
    group_names_list: list
) -> Any:
    """
    Create pile group definitions and assignments for SAFE16 analysis organization.
    Sets up analysis groups for logical pile element organization in SAFE16.
    """    
    # Helper function for creating group assignments in SAFE16 format
    def add_group_assignments_s16(group_name: str, obj_type: str, obj_labels_s16: Union[pd.Series, list]) -> None:
        """
        Add objects to a SAFE16 group.
        """
        if not isinstance(obj_labels_s16, pd.Series):
            obj_labels_s16 = pd.Series(obj_labels_s16)

        if obj_labels_s16.empty:
            return

        data_s16 = {
            ('TABLE:  Group Assignments', 'Group', 'Text'): group_name,
            ('TABLE:  Group Assignments', 'ObjType', 'Text'): obj_type,
            ('TABLE:  Group Assignments', 'ObjLabel', 'Text'): obj_labels_s16.values
        }
        df_append_s16 = pd.DataFrame(data_s16)
        safe16_dfs.GroupAssignments = pd.concat(
            [safe16_dfs.GroupAssignments, df_append_s16], ignore_index=True
        )

    # Create group definitions with standard color coding    # Create group definitions with standard color coding
    data_s16_group_def = {
        ('TABLE:  Group Definitions', 'Group', 'Text'): group_names_list,
        ('TABLE:  Group Definitions', 'Color', 'Text'): 'Yellow'
    }
    df_append_s16_group_def = pd.DataFrame(data_s16_group_def)
    safe16_dfs.GroupDefinitions = pd.concat(
        [safe16_dfs.GroupDefinitions, df_append_s16_group_def], ignore_index=True
    )

    # Category A: General pile groupings for bulk operations
    add_group_assignments_s16('A.Pile_Top', 'Point', top_points)
    add_group_assignments_s16('A.Pile_Top', 'Line', top_points)

    add_group_assignments_s16('A.Pile_Bottom', 'Point', bottom_points)
    add_group_assignments_s16('A.Pile_Bottom', 'Line', bottom_points)

    add_group_assignments_s16('A.Pile_Line', 'Line', line_segments_for_pile_line_group)
    add_group_assignments_s16('A.Pile_Point', 'Point', all_point_names)

    # Category B: Individual pile groupings for selective analysis
    # Group points by individual pile (e.g., B.PilePoint_P1, B.PilePoint_P2)
    base_pile_ids_from_points = all_point_names.str.split('_').str[0]
    group_names_for_pile_points = 'B.PilePoint_' + base_pile_ids_from_points
    add_group_assignments_s16(group_names_for_pile_points, 'Point', all_point_names)

    # Group lines by individual pile (e.g., B.PileLine_P1, B.PileLine_P2)
    base_pile_ids_from_line_segments = line_segments_for_pile_line_group.str.split('_').str[0]
    group_names_for_pile_lines = 'B.PileLine_' + base_pile_ids_from_line_segments
    add_group_assignments_s16(group_names_for_pile_lines, 'Line', line_segments_for_pile_line_group)

    return safe16_dfs


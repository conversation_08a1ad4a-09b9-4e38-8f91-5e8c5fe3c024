"""
Base plotting functions for DXF visualization.

This module contains fundamental plotting utilities and site boundary plotting.
"""

from typing import Optional
from shapely.geometry import Polygon


# Common constants
DEFAULT_TEXT_HEIGHT = 0.4
DEFAULT_EDGE_DISTANCE = 0.4
DEFAULT_PILE_RADIUS = 0.3


def plot_site_boundary_dxf(msp, site_boundary: Polygon, log_callback=None) -> None:    
    """
    Plot the actual site boundary as a polyline.
    
    Args:
        msp: DXF model space
        site_boundary: Shapely Polygon representing the site boundary
        log_callback: Optional logging callback function
    """
    try:
        if not site_boundary or site_boundary.is_empty:
            if log_callback:
                log_callback("Site boundary is empty")
            return
            
        # Handle both simple Polygon and MultiPolygon cases
        if hasattr(site_boundary, 'geoms'):
            # MultiPolygon case - plot all polygons
            for i, poly in enumerate(site_boundary.geoms):
                if isinstance(poly, Polygon) and not poly.is_empty:
                    coords = list(poly.exterior.coords)
                    if len(coords) >= 3:
                        polyline = msp.add_lwpolyline(coords[:-1])  # Exclude duplicate last point
                        polyline.close()
                        polyline.dxf.layer = 'SITE_BOUNDARY'
        else:            
            # Single Polygon case
            coords = list(site_boundary.exterior.coords)
            if len(coords) >= 3:
                # Create polyline for site boundary
                polyline = msp.add_lwpolyline(coords[:-1])  # Exclude duplicate last point
                polyline.close()
                polyline.dxf.layer = 'SITE_BOUNDARY'
                
                if log_callback:
                    log_callback(f"Site boundary plotted with {len(coords)-1} vertices")
            else:
                if log_callback:
                    log_callback("Site boundary has insufficient vertices")
            
    except Exception as e:
        if log_callback:
            log_callback(f"Error plotting site boundary: {e}")


def add_cross_marker(msp, x: float, y: float, size: float, layer: str) -> None:
    """
    Add a cross marker at the specified position.
    
    Args:
        msp: DXF model space
        x, y: Coordinates for the cross marker
        size: Size of the cross marker
        layer: DXF layer name for the marker
    """
    half_size = size / 2
    
    # Horizontal line
    msp.add_line(
        (x - half_size, y), (x + half_size, y),
        dxfattribs={'layer': layer}
    )
    
    # Vertical line
    msp.add_line(
        (x, y - half_size), (x, y + half_size),
        dxfattribs={'layer': layer}
    )


def create_polygon_from_coords(msp, coords: list, layer: str, close: bool = True):
    """
    Create a polygon from coordinates with proper validation.
    
    Args:
        msp: DXF model space
        coords: List of coordinate tuples
        layer: DXF layer name
        close: Whether to close the polygon
        
    Returns:
        The created polyline entity or None if failed
    """
    try:
        if len(coords) < 3:
            return None
            
        # Remove duplicate last point if it exists
        if close and len(coords) > 1 and coords[0] == coords[-1]:
            coords = coords[:-1]
            
        polyline = msp.add_lwpolyline(coords)
        if close:
            polyline.close()
        polyline.dxf.layer = layer
        
        return polyline
        
    except Exception:
        return None


def validate_coordinates(coords: list, min_points: int = 2) -> bool:
    """
    Validate coordinate list for plotting.
    
    Args:
        coords: List of coordinate tuples
        min_points: Minimum number of points required
        
    Returns:
        True if coordinates are valid, False otherwise
    """
    if not coords or len(coords) < min_points:
        return False
        
    for coord in coords:
        if not isinstance(coord, (list, tuple)) or len(coord) < 2:
            return False
        try:
            float(coord[0])
            float(coord[1])
        except (ValueError, TypeError):
            return False
            
    return True

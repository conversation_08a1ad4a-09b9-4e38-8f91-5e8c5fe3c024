"""
Data Writing Operations for ETABS CWLS Module

This module provides functions for writing data to Excel files with professional
formatting. It includes comprehensive error handling, validation, and enhanced
logging for all write operations.

Functions:
    write_excel: Write DataFrame to Excel with proper formatting
    format_load_schedule_excel: Apply professional formatting to load schedules
    remove_column1_and_row3: Legacy function for data cleanup
    validate_excel_write_access: Validate write permissions for Excel files
"""

import pandas as pd
import os
import openpyxl
from openpyxl.utils import get_column_letter
from openpyxl.styles import Alignment, Border, Side
from typing import Optional, List, Dict, Any, Callable, Union
from pathlib import Path

from .exceptions import FileOperationError, DataValidationError
from .logging_config import (
    enhanced_log, log_function_entry, log_function_exit,
    log_validation_result, log_calculation_result, log_error_with_context,
    create_timed_logger, log_performance_metric
)



def write_excel(
    df: pd.DataFrame,
    path_excel_output: str,
    sheet: str,
    log_callback: Optional[Callable] = None
) -> pd.DataFrame:
    """
    Write DataFrame to Excel file with proper error handling and logging.

    This function writes a pandas DataFrame to an Excel file, handling both
    new file creation and updating existing files. It automatically detects
    multi-level column headers and adjusts the output format accordingly.

    Args:
        df: DataFrame to write to Excel
        path_excel_output: Path to the output Excel file
        sheet: Name of the worksheet to write to
        log_callback: Optional logging callback function

    Returns:
        pandas.DataFrame: The original DataFrame that was written

    Raises:
        DataValidationError: If input parameters are invalid
        FileOperationError: If file write operation fails

    Example:
        >>> df = pd.DataFrame({'A': [1, 2], 'B': [3, 4]})
        >>> write_excel(df, "output.xlsx", "Data")
    """
    log_function_entry(log_callback, "write_excel",
                      rows=len(df), columns=len(df.columns),
                      path=path_excel_output, sheet=sheet)

    try:
        # Input validation
        if not isinstance(df, pd.DataFrame):
            raise DataValidationError(
                f"Input must be a pandas DataFrame, got {type(df).__name__}",
                field_name="df",
                invalid_value=type(df).__name__,
                expected_type="pandas.DataFrame",
                error_code="INVALID_DATAFRAME_TYPE"
            )

        if df.empty:
            enhanced_log(log_callback, "Warning: Writing empty DataFrame to Excel", 'WARNING')

        if not isinstance(path_excel_output, str) or not path_excel_output.strip():
            raise DataValidationError(
                "Excel output path must be a non-empty string",
                field_name="path_excel_output",
                invalid_value=path_excel_output,
                expected_type="str",
                error_code="INVALID_EXCEL_PATH"
            )

        if not isinstance(sheet, str) or not sheet.strip():
            raise DataValidationError(
                "Sheet name must be a non-empty string",
                field_name="sheet",
                invalid_value=sheet,
                expected_type="str",
                error_code="INVALID_SHEET_NAME"
            )

        # Validate sheet name length (Excel limit is 31 characters)
        if len(sheet) > 31:
            raise DataValidationError(
                f"Sheet name too long (max 31 characters): '{sheet}' ({len(sheet)} characters)",
                field_name="sheet",
                invalid_value=sheet,
                error_code="SHEET_NAME_TOO_LONG"
            )

        # Validate sheet name characters (Excel doesn't allow certain characters)
        invalid_chars = ['\\', '/', '*', '?', ':', '[', ']']
        for char in invalid_chars:
            if char in sheet:
                raise DataValidationError(
                    f"Sheet name contains invalid character '{char}': '{sheet}'",
                    field_name="sheet",
                    invalid_value=sheet,
                    error_code="INVALID_SHEET_NAME_CHARS"
                )

        abs_path = os.path.abspath(path_excel_output)
        output_dir = os.path.dirname(abs_path)

        # Ensure output directory exists
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir, exist_ok=True)
                enhanced_log(log_callback, f"Created output directory: {output_dir}", 'INFO')
            except OSError as e:
                raise FileOperationError(
                    f"Cannot create output directory: {output_dir}",
                    file_path=output_dir,
                    operation="create_directory",
                    error_code="DIRECTORY_CREATE_FAILED"
                ) from e

        # Check write permissions
        if os.path.exists(abs_path) and not os.access(abs_path, os.W_OK):
            raise FileOperationError(
                f"No write permission for Excel file: {abs_path}",
                file_path=abs_path,
                operation="write",
                error_code="EXCEL_WRITE_PERMISSION_DENIED"
            )

        enhanced_log(log_callback, f"Writing {len(df)} rows to sheet '{sheet}' in: {abs_path}", 'INFO')

        # Determine index setting based on column structure
        use_index = df.columns.nlevels > 1
        log_calculation_result(log_callback, "column_levels", df.columns.nlevels, "levels")
        log_calculation_result(log_callback, "use_index", use_index, "boolean")

        # Write to Excel with timing
        with create_timed_logger(log_callback, "excel_write") as timer:
            try:
                if os.path.isfile(abs_path):
                    # File exists - append/replace sheet
                    enhanced_log(log_callback, f"Updating existing Excel file", 'DEBUG')

                    with pd.ExcelWriter(abs_path, mode='a', engine='openpyxl',
                                      if_sheet_exists='replace') as writer:
                        df.to_excel(writer, index=use_index, sheet_name=sheet)
                        enhanced_log(log_callback, f"Sheet '{sheet}' updated in existing file", 'DEBUG')
                else:
                    # New file - create
                    enhanced_log(log_callback, f"Creating new Excel file", 'DEBUG')
                    df.to_excel(abs_path, sheet_name=sheet, index=use_index)
                    enhanced_log(log_callback, f"New Excel file created with sheet '{sheet}'", 'DEBUG')

            except PermissionError as e:
                raise FileOperationError(
                    f"Permission denied writing to Excel file: {abs_path}. "
                    f"Please close the file if it's open in Excel.",
                    file_path=abs_path,
                    operation="write",
                    error_code="EXCEL_FILE_LOCKED"
                ) from e

            except Exception as e:
                raise FileOperationError(
                    f"Failed to write Excel file: {str(e)}",
                    file_path=abs_path,
                    operation="write",
                    error_code="EXCEL_WRITE_FAILED"
                ) from e

        # Verify the write was successful
        if os.path.exists(abs_path):
            file_size = os.path.getsize(abs_path)
            log_performance_metric(log_callback, "output_file_size", file_size, "bytes")
            enhanced_log(log_callback, f"Excel file written successfully ({file_size} bytes)", 'INFO')

        log_validation_result(log_callback, "excel_write", True,
                            f"Successfully wrote {len(df)} rows to '{sheet}'")
        log_function_exit(log_callback, "write_excel", f"Success - {len(df)} rows written")

        return df

    except Exception as e:
        log_error_with_context(log_callback, e, f"write_excel(sheet='{sheet}', path='{path_excel_output}')")
        log_validation_result(log_callback, "excel_write", False, str(e))
        raise


def remove_column1_and_row3(
    excel_file: str,
    sheet_name: str,
    log_callback: Optional[Callable] = None
) -> None:
    """
    Legacy function to remove first column and third row from Excel sheet.

    Note: This function is deprecated and maintained for backward compatibility.
    New code should use more specific data processing functions.

    Args:
        excel_file: Path to the Excel file
        sheet_name: Name of the sheet to modify
        log_callback: Optional logging callback function

    Raises:
        FileOperationError: If file operations fail
        DataValidationError: If input parameters are invalid
    """
    log_function_entry(log_callback, "remove_column1_and_row3",
                      excel_file=excel_file, sheet_name=sheet_name)

    enhanced_log(log_callback, "Warning: Using deprecated function remove_column1_and_row3", 'WARNING')

    try:
        # Input validation
        if not isinstance(excel_file, str) or not excel_file.strip():
            raise DataValidationError(
                "Excel file path must be a non-empty string",
                field_name="excel_file",
                invalid_value=excel_file,
                expected_type="str",
                error_code="INVALID_EXCEL_PATH"
            )

        if not isinstance(sheet_name, str) or not sheet_name.strip():
            raise DataValidationError(
                "Sheet name must be a non-empty string",
                field_name="sheet_name",
                invalid_value=sheet_name,
                expected_type="str",
                error_code="INVALID_SHEET_NAME"
            )

        abs_path = os.path.abspath(excel_file)

        # Check file exists
        if not os.path.exists(abs_path):
            raise FileOperationError(
                f"Excel file not found: {abs_path}",
                file_path=abs_path,
                operation="modify",
                error_code="EXCEL_FILE_NOT_FOUND"
            )

        enhanced_log(log_callback, f"Modifying sheet '{sheet_name}' in: {abs_path}", 'INFO')

        with create_timed_logger(log_callback, "excel_modification") as timer:
            try:
                # Read the Excel file into a DataFrame
                df = pd.read_excel(abs_path, sheet_name=sheet_name)
                original_shape = df.shape
                log_calculation_result(log_callback, "original_rows", original_shape[0], "rows")
                log_calculation_result(log_callback, "original_columns", original_shape[1], "columns")

                # Remove the first column
                if df.shape[1] > 0:
                    df = df.iloc[:, 1:]
                    enhanced_log(log_callback, "Removed first column", 'DEBUG')

                # Remove the third row (index 1, since 0-based indexing)
                if len(df) > 1:
                    df = df.drop(df.index[1])
                    enhanced_log(log_callback, "Removed third row (index 1)", 'DEBUG')

                final_shape = df.shape
                log_calculation_result(log_callback, "final_rows", final_shape[0], "rows")
                log_calculation_result(log_callback, "final_columns", final_shape[1], "columns")

                # Save the modified DataFrame back to the Excel file
                with pd.ExcelWriter(abs_path, mode='a', engine='openpyxl',
                                  if_sheet_exists='replace') as writer:
                    df.to_excel(writer, index=False, sheet_name=sheet_name)

                enhanced_log(log_callback, f"Sheet modification completed: {original_shape} -> {final_shape}", 'INFO')

            except Exception as e:
                raise FileOperationError(
                    f"Failed to modify Excel sheet: {str(e)}",
                    file_path=abs_path,
                    operation="modify",
                    error_code="EXCEL_MODIFICATION_FAILED"
                ) from e

        log_validation_result(log_callback, "excel_modification", True,
                            f"Successfully modified sheet '{sheet_name}'")
        log_function_exit(log_callback, "remove_column1_and_row3", "Success")

    except Exception as e:
        log_error_with_context(log_callback, e, f"remove_column1_and_row3('{excel_file}', '{sheet_name}')")
        log_validation_result(log_callback, "excel_modification", False, str(e))
        raise


def format_load_schedule_excel(
    excel_file: str,
    excel_sheet: str,
    loadcase_list: List[str],
    consider_torsion: bool = True,
    log_callback: Optional[Callable] = None
) -> None:
    """
    Apply professional formatting to load schedule Excel sheets.

    This function formats load schedule Excel sheets with proper headers,
    merged cells for load cases, and professional styling including borders
    and alignment. It creates a structured layout suitable for engineering
    documentation.

    Args:
        excel_file: Path to the Excel file to format
        excel_sheet: Name of the sheet to format
        loadcase_list: List of load case names
        consider_torsion: Whether to include torsion (Mz) columns
        log_callback: Optional logging callback function

    Raises:
        FileOperationError: If file operations fail
        DataValidationError: If input parameters are invalid

    Example:
        >>> format_load_schedule_excel("output.xlsx", "Load Schedule",
        ...                          ["DL", "LL", "W1"], consider_torsion=True)
    """
    log_function_entry(log_callback, "format_load_schedule_excel",
                      excel_file=excel_file, excel_sheet=excel_sheet,
                      loadcase_count=len(loadcase_list), consider_torsion=consider_torsion)

    try:
        # Input validation
        if not isinstance(excel_file, str) or not excel_file.strip():
            raise DataValidationError(
                "Excel file path must be a non-empty string",
                field_name="excel_file",
                invalid_value=excel_file,
                expected_type="str",
                error_code="INVALID_EXCEL_PATH"
            )

        if not isinstance(excel_sheet, str) or not excel_sheet.strip():
            raise DataValidationError(
                "Excel sheet name must be a non-empty string",
                field_name="excel_sheet",
                invalid_value=excel_sheet,
                expected_type="str",
                error_code="INVALID_SHEET_NAME"
            )

        if not isinstance(loadcase_list, list) or not loadcase_list:
            raise DataValidationError(
                "Load case list must be a non-empty list",
                field_name="loadcase_list",
                invalid_value=loadcase_list,
                expected_type="list",
                error_code="INVALID_LOADCASE_LIST"
            )

        if not isinstance(consider_torsion, bool):
            raise DataValidationError(
                "consider_torsion must be a boolean",
                field_name="consider_torsion",
                invalid_value=consider_torsion,
                expected_type="bool",
                error_code="INVALID_TORSION_FLAG"
            )

        abs_path = os.path.abspath(excel_file)

        # Check file exists
        if not os.path.exists(abs_path):
            raise FileOperationError(
                f"Excel file not found: {abs_path}",
                file_path=abs_path,
                operation="format",
                error_code="EXCEL_FILE_NOT_FOUND"
            )

        enhanced_log(log_callback, f"Formatting load schedule sheet '{excel_sheet}' in: {abs_path}", 'INFO')

        # Determine number of columns per loadcase
        ncol = 6 if consider_torsion else 5
        load_types = ["Vx (kN)", "Vy (kN)", "Axial (kN)", "Mx (kNm)", "My (kNm)"]
        if consider_torsion:
            load_types.append("Mz (kNm)")

        log_calculation_result(log_callback, "columns_per_loadcase", ncol, "columns")
        log_calculation_result(log_callback, "total_loadcases", len(loadcase_list), "loadcases")
        log_calculation_result(log_callback, "total_data_columns", len(loadcase_list) * ncol, "columns")

        with create_timed_logger(log_callback, "excel_formatting") as timer:
            try:
                # Load workbook and worksheet
                wb = openpyxl.load_workbook(abs_path)

                if excel_sheet not in wb.sheetnames:
                    raise FileOperationError(
                        f"Sheet '{excel_sheet}' not found in Excel file",
                        file_path=abs_path,
                        operation="format",
                        error_code="SHEET_NOT_FOUND"
                    )

                ws = wb[excel_sheet]
                enhanced_log(log_callback, f"Loaded worksheet '{excel_sheet}'", 'DEBUG')

                # 1. Insert a new row at the top for load case headers
                ws.insert_rows(1)
                enhanced_log(log_callback, "Inserted header row", 'DEBUG')

                # Set up load case headers with merged cells
                ws.cell(row=1, column=1, value=None)  # Empty cell above "Pier"
                col = 2

                for i, loadcase in enumerate(loadcase_list):
                    start_col = col
                    end_col = col + ncol - 1

                    # Merge cells for load case header
                    ws.merge_cells(start_row=1, start_column=start_col, end_row=1, end_column=end_col)

                    # Set load case name and center alignment
                    cell = ws.cell(row=1, column=start_col, value=loadcase)
                    cell.alignment = Alignment(horizontal="center", vertical="center")

                    col += ncol
                    enhanced_log(log_callback, f"Added header for load case '{loadcase}' (columns {start_col}-{end_col})", 'DEBUG')

                # 2. Set up column headers in the second row
                ws.cell(row=2, column=1, value="Pier")
                col = 2

                for loadcase in loadcase_list:
                    for load_type in load_types:
                        ws.cell(row=2, column=col, value=load_type)
                        col += 1

                enhanced_log(log_callback, f"Added column headers for {len(load_types)} load types", 'DEBUG')

                # 3. Apply borders to all cells with content
                thin = Side(border_style="thin", color="000000")
                border = Border(left=thin, right=thin, top=thin, bottom=thin)

                max_row = ws.max_row
                max_col = ws.max_column

                for row in ws.iter_rows(min_row=1, max_row=max_row, min_col=1, max_col=max_col):
                    for cell in row:
                        cell.border = border

                log_calculation_result(log_callback, "formatted_rows", max_row, "rows")
                log_calculation_result(log_callback, "formatted_columns", max_col, "columns")
                enhanced_log(log_callback, f"Applied borders to {max_row}x{max_col} cell range", 'DEBUG')

                # Save the formatted workbook
                wb.save(abs_path)
                enhanced_log(log_callback, "Saved formatted Excel file", 'DEBUG')

            except PermissionError as e:
                raise FileOperationError(
                    f"Permission denied formatting Excel file: {abs_path}. "
                    f"Please close the file if it's open in Excel.",
                    file_path=abs_path,
                    operation="format",
                    error_code="EXCEL_FILE_LOCKED"
                ) from e

            except Exception as e:
                raise FileOperationError(
                    f"Failed to format Excel file: {str(e)}",
                    file_path=abs_path,
                    operation="format",
                    error_code="EXCEL_FORMAT_FAILED"
                ) from e

        enhanced_log(log_callback, f"Load schedule formatting completed successfully", 'INFO')
        log_validation_result(log_callback, "excel_formatting", True,
                            f"Successfully formatted sheet '{excel_sheet}' with {len(loadcase_list)} load cases")
        log_function_exit(log_callback, "format_load_schedule_excel", "Success")

    except Exception as e:
        log_error_with_context(log_callback, e, f"format_load_schedule_excel('{excel_file}', '{excel_sheet}')")
        log_validation_result(log_callback, "excel_formatting", False, str(e))
        raise


def validate_excel_write_access(
    file_path: str,
    log_callback: Optional[Callable] = None
) -> bool:
    """
    Validate write access to an Excel file.

    Args:
        file_path: Path to the Excel file
        log_callback: Optional logging callback function

    Returns:
        True if write access is available

    Raises:
        FileOperationError: If write access validation fails
    """
    log_function_entry(log_callback, "validate_excel_write_access", file_path=file_path)

    try:
        abs_path = os.path.abspath(file_path)

        if os.path.exists(abs_path):
            # File exists - check write permission
            if not os.access(abs_path, os.W_OK):
                raise FileOperationError(
                    f"No write permission for Excel file: {abs_path}",
                    file_path=abs_path,
                    operation="validate_write",
                    error_code="EXCEL_WRITE_PERMISSION_DENIED"
                )

            # Try to open the file to check if it's locked
            try:
                with open(abs_path, 'r+b'):
                    pass
            except PermissionError as e:
                raise FileOperationError(
                    f"Excel file is locked (possibly open in Excel): {abs_path}",
                    file_path=abs_path,
                    operation="validate_write",
                    error_code="EXCEL_FILE_LOCKED"
                ) from e
        else:
            # File doesn't exist - check directory write permission
            directory = os.path.dirname(abs_path)
            if not os.access(directory, os.W_OK):
                raise FileOperationError(
                    f"No write permission for directory: {directory}",
                    file_path=directory,
                    operation="validate_write",
                    error_code="DIRECTORY_WRITE_PERMISSION_DENIED"
                )

        enhanced_log(log_callback, f"Excel write access validated: {abs_path}", 'DEBUG')
        log_validation_result(log_callback, "excel_write_access", True, "Write access confirmed")
        log_function_exit(log_callback, "validate_excel_write_access", True)
        return True

    except Exception as e:
        log_error_with_context(log_callback, e, f"validate_excel_write_access('{file_path}')")
        log_validation_result(log_callback, "excel_write_access", False, str(e))
        raise


# Export all functions
__all__ = [
    'write_excel',
    'format_load_schedule_excel',
    'remove_column1_and_row3',
    'validate_excel_write_access'
]

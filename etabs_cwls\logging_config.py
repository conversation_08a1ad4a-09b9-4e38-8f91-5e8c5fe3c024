"""
Enhanced Logging Configuration for ETABS CWLS Module

This module provides comprehensive logging configuration with external log file storage,
enhanced logging patterns, and backward compatibility with existing log_callback functions.

Features:
- External log file storage (temp directory, user documents)
- Enhanced logging system with explicit levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- Function entry/exit logging
- Performance metrics tracking
- Validation result logging
- Error context logging
- Backward compatibility with legacy log_callback functions

The logging system follows the patterns established in the Foundation Automation
enhanced logging system while providing ETABS CWLS-specific functionality.
"""

import logging
import os
import tempfile
import time
from pathlib import Path
from typing import Optional, Callable, Any, Union
import inspect

# Enhanced logging system imports with fallback
try:
    from fdn_agent.pile_estimation.utils.logging_utils import (
        enhanced_log,
        log_function_entry,
        log_function_exit,
        log_validation_result,
        log_calculation_result,
        log_performance_metric,
        log_error_with_context,
        log_constraint_check,
        create_timed_logger
    )
    ENHANCED_LOGGING_AVAILABLE = True
except ImportError:
    ENHANCED_LOGGING_AVAILABLE = False


def get_external_log_directory() -> Path:
    """
    Get external log directory for storing log files outside the application directory.
    
    Priority order:
    1. User Documents/Foundation_Automation_Logs
    2. System temp directory/Foundation_Automation_Logs
    3. Current user temp directory/Foundation_Automation_Logs
    
    Returns:
        Path: Path to the external log directory
        
    Raises:
        OSError: If unable to create log directory
    """
    try:
        # Try user documents directory first
        if os.name == 'nt':  # Windows
            documents_path = Path.home() / "Documents" / "Foundation_Automation_Logs" / "etabs_cwls"
        else:  # Unix-like systems
            documents_path = Path.home() / "Foundation_Automation_Logs" / "etabs_cwls"
        
        documents_path.mkdir(parents=True, exist_ok=True)
        
        # Test write permissions
        test_file = documents_path / "test_write.tmp"
        test_file.write_text("test")
        test_file.unlink()
        
        return documents_path
        
    except (OSError, PermissionError):
        try:
            # Fallback to system temp directory
            temp_path = Path(tempfile.gettempdir()) / "Foundation_Automation_Logs" / "etabs_cwls"
            temp_path.mkdir(parents=True, exist_ok=True)
            
            # Test write permissions
            test_file = temp_path / "test_write.tmp"
            test_file.write_text("test")
            test_file.unlink()
            
            return temp_path
            
        except (OSError, PermissionError):
            # Final fallback to user temp directory
            user_temp_path = Path(tempfile.mkdtemp()) / "Foundation_Automation_Logs" / "etabs_cwls"
            user_temp_path.mkdir(parents=True, exist_ok=True)
            return user_temp_path


def setup_enhanced_logging() -> tuple[logging.Logger, Path]:
    """
    Set up enhanced logging configuration with external log file storage.
    
    Returns:
        tuple[logging.Logger, Path]: Configured logger and log file path
        
    Raises:
        OSError: If unable to set up logging
    """
    try:
        # Get external log directory
        log_dir = get_external_log_directory()
        
        # Create log file with timestamp
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"etabs_cwls_{timestamp}.log"
        
        # Configure logging
        logger = logging.getLogger('etabs_cwls')
        logger.setLevel(logging.DEBUG)
        
        # Remove existing handlers to avoid duplicates
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler for all levels
        file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # Console handler for ERROR and above only
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.ERROR)
        console_formatter = logging.Formatter('%(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # Prevent propagation to root logger
        logger.propagate = False
        
        logger.info(f"Enhanced logging initialized - Log file: {log_file}")
        
        return logger, log_file
        
    except Exception as e:
        # Fallback to basic logging if enhanced setup fails
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        logger = logging.getLogger('etabs_cwls')
        logger.error(f"Failed to set up enhanced logging: {e}")
        return logger, None


# Global logger instance
_logger, _log_file_path = setup_enhanced_logging()


def get_logger() -> logging.Logger:
    """Get the configured logger instance."""
    return _logger


def get_log_file_path() -> Optional[Path]:
    """Get the current log file path."""
    return _log_file_path


# Enhanced logging functions with fallback implementations
if not ENHANCED_LOGGING_AVAILABLE:
    def enhanced_log(log_callback: Optional[Callable], message: str, level: str = 'INFO') -> None:
        """Fallback enhanced logging function."""
        if log_callback:
            try:
                # Check if callback accepts level parameter
                sig = inspect.signature(log_callback)
                if len(sig.parameters) >= 2:
                    log_callback(message, level)
                else:
                    log_callback(f"[{level}] {message}")
            except Exception:
                log_callback(message)
        
        # Also log to our logger
        logger = get_logger()
        getattr(logger, level.lower(), logger.info)(message)

    def log_function_entry(log_callback: Optional[Callable], function_name: str, **kwargs) -> None:
        """Fallback function entry logging."""
        if kwargs:
            params_str = ", ".join(f"{k}={v}" for k, v in kwargs.items())
            if len(params_str) > 100:
                params_str = params_str[:97] + "..."
            message = f"! Entering {function_name}({params_str})"
        else:
            message = f"! Entering {function_name}()"
        enhanced_log(log_callback, message, 'DEBUG')

    def log_function_exit(log_callback: Optional[Callable], function_name: str, result: Any = None, **kwargs) -> None:
        """Fallback function exit logging."""
        message_parts = [f"! Exiting {function_name}()"]
        if result is not None:
            message_parts.append(f"-> {result}")
        if kwargs:
            params_str = ", ".join(f"{k}={v}" for k, v in kwargs.items())
            if len(params_str) > 100:
                params_str = params_str[:97] + "..."
            message_parts.append(f"({params_str})")
        enhanced_log(log_callback, " ".join(message_parts), 'DEBUG')

    def log_validation_result(log_callback: Optional[Callable], validation_name: str, passed: bool, details: str = '') -> None:
        """Fallback validation result logging."""
        status = "PASSED" if passed else "FAILED"
        level = 'INFO' if passed else 'WARNING'
        message = f"Validation - {validation_name}: {status}"
        if details:
            message += f" - {details}"
        enhanced_log(log_callback, message, level)

    def log_calculation_result(log_callback: Optional[Callable], calculation_name: str, result: Any, unit: str = '') -> None:
        """Fallback calculation result logging."""
        unit_str = f" {unit}" if unit else ""
        enhanced_log(log_callback, f"Calculation - {calculation_name}: {result}{unit_str}", 'INFO')

    def log_performance_metric(log_callback: Optional[Callable], metric_name: str, value: Union[float, int], unit: str = '') -> None:
        """Fallback performance metric logging."""
        unit_str = f" {unit}" if unit else ""
        if isinstance(value, float):
            enhanced_log(log_callback, f"Performance - {metric_name}: {value:.3f}{unit_str}", 'DEBUG')
        else:
            enhanced_log(log_callback, f"Performance - {metric_name}: {value}{unit_str}", 'DEBUG')

    def log_error_with_context(log_callback: Optional[Callable], error: Exception, context: str) -> None:
        """Fallback error context logging."""
        error_msg = f"Error in {context}: {type(error).__name__}: {str(error)}"
        enhanced_log(log_callback, error_msg, 'ERROR')

    def log_constraint_check(log_callback: Optional[Callable], constraint_name: str, value: Any, limit: Any, satisfied: bool) -> None:
        """Fallback constraint check logging."""
        status = "OK" if satisfied else "VIOLATED"
        level = 'DEBUG' if satisfied else 'WARNING'
        enhanced_log(log_callback, f"Constraint - {constraint_name}: {value} vs {limit} [{status}]", level)

    class TimedLogger:
        """Fallback timed logger context manager."""
        def __init__(self, log_callback: Optional[Callable], operation_name: str):
            self.log_callback = log_callback
            self.operation_name = operation_name
            self.start_time = None
            self.end_time = None

        def __enter__(self):
            self.start_time = time.time()
            enhanced_log(self.log_callback, f"Starting {self.operation_name}", 'DEBUG')
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            self.end_time = time.time()
            duration = self.end_time - self.start_time
            if exc_type is None:
                log_performance_metric(self.log_callback, f"{self.operation_name}_duration", duration, "seconds")
                enhanced_log(self.log_callback, f"Completed {self.operation_name} in {duration:.3f}s", 'INFO')
            else:
                enhanced_log(self.log_callback, f"Failed {self.operation_name} after {duration:.3f}s: {exc_val}", 'ERROR')

        def get_duration(self) -> Optional[float]:
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None

    def create_timed_logger(log_callback: Optional[Callable], operation_name: str) -> TimedLogger:
        """Fallback timed logger creation."""
        return TimedLogger(log_callback, operation_name)


# Export all logging functions
__all__ = [
    'get_logger',
    'get_log_file_path',
    'get_external_log_directory',
    'setup_enhanced_logging',
    'enhanced_log',
    'log_function_entry',
    'log_function_exit',
    'log_validation_result',
    'log_calculation_result',
    'log_performance_metric',
    'log_error_with_context',
    'log_constraint_check',
    'create_timed_logger'
]

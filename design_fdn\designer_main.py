"""
Foundation Design Automation - Main Business Logic and Workflow Orchestration

This module serves as the central orchestrator for comprehensive foundation design automation,
implementing the main business logic for pile foundation analysis, design verification, and
optimization workflows. It coordinates the complete design process from initial data processing
through Ultimate Limit State (ULS) and Serviceability Limit State (SLS) verification to final
design optimization and reporting.

The module provides comprehensive foundation design automation including:
- Multi-pile type design support: <PERSON><PERSON> (BP), Socket H-Piles (SHP), Driven H-Piles (DHP), <PERSON> Piles (MP)
- Integrated ULS and SLS analysis workflows with SAFE structural analysis integration
- Automated pile capacity calculations and geotechnical design verification
- Advanced design optimization with iterative refinement capabilities
- Comprehensive design check procedures following international foundation design codes
- Multi-format output generation for engineering review and regulatory compliance

Author: Foundation Design Automation Team
Version: 2.0
Last Updated: 2024
"""
import sys
from pathlib import Path
import os
from typing import Optional, Callable, Any, Dict, Set, List, Tuple, Union
import pandas as pd

# Use __init__.py imports for design_fdn modules
from design_fdn import (
    cal_pile_sls,
    cal_pile_uls,
    cal_pile_uls_pdelta,
    cal_settlement,
    cal_pile_local_deflection,
    process_nsf
)
from design_fdn.sls_design import (
    check_slab_angular_rotation_xy,
    cal_lateral_displacement,
    check_pile_deflection,
    check_differential_settlement
)
# from design_fdn.data_processing import cal_pile_local_deflection  # Moved to main import to avoid circular dependency

# Add the parent directory (workspace root) to sys.path
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
if str(parent_dir) not in sys.path:
    sys.path.insert(0, str(parent_dir))

# Import configuration constants
from design_fdn.design_fdn_config import (
    # Input file names
    EXCEL_PROPERTY_FILENAME, EXCEL_GEOMETRY_FILENAME, EXCEL_GEOLOGY_FILENAME, EXCEL_LOADING_FILENAME,
    
    # Main result file names
    DESIGN_LOG_FILENAME, DESIGN_LOAD_FILENAME, PILE_CAPACITY_FILENAME, PILING_SCHEDULE_FILENAME,
    
    # ULS result file names
    PILE_ULS_SQLITE_FILENAME, PILE_SLS_SQLITE_FILENAME, 
    PILE_ULS_PDELTA_SQLITE_FILENAME, PILE_ULS_PDELTA_CAL_SQLITE_FILENAME,
    STEEL_HPILE_CHECK_BD_SQLITE_FILENAME,
    STEEL_HPILE_CHECK_ASD_SQLITE_FILENAME, PILE_NEEDS_PDELTA_FILENAME, BP_SHEAR_CHECK_FILENAME,
    BP_SHEAR_DESIGN_FILENAME, BP_SEGMENT_REBAR_LOG_FILENAME, BP_REBAR_LOG_FILENAME,
    SHP_CHECK_FILENAME, DHP_CHECK_FILENAME,
    
    # SLS result file names
    PILE_LOCAL_XY_SQLITE_FILENAME, SETTLEMENT_SQLITE_FILENAME, LATERAL_DISPLACEMENT_SQLITE_FILENAME,
    PILE_DEFLECTION_CHECK_FILENAME, PILE_DEFLECTION_FAIL_FILENAME, ANGULAR_ROTATION_CHECK_FILENAME,
    ANGULAR_ROTATION_FAIL_FILENAME, DIFFERENTIAL_SETTLEMENT_CHECK_FILENAME, DIFFERENTIAL_SETTLEMENT_FAIL_FILENAME,
    
    # Folder names
    ANALYTICAL_RESULTS_FOLDER, ULS_FOLDER, SLS_FOLDER, BP_PROKON_FOLDER, BP_NM_DESIGN_DETAIL_FOLDER
)

import main_class as main_class
from design_fdn import (
    cal_capacity_piles,
    gen_piling_schedule,
    cal_stepping_effect,
    init_excel_bp_rebar,
    init_excel_bp_segment_rebar
)
from design_fdn import read as _read
from design_fdn.uls_design_hp import check_steel_h as _check_steel_h
from design_fdn.uls_design_bp import (
    gen_all_c13,
    design_all_bp_segment_nm,
    group_bp_segment_rebar,
    check_bp_shear,
    design_bp_shear,
    combine_segment_pngs_for_pile,
    combine_segment_excels_for_pile
)
from read.read_geology import read_input_geology
from read.read_geometry import read_input_geometry
from read.read_loading import read_input_loading
from read.read_property import read_input_property
from read.read_steel import read_input_steel



def initialize_file_paths(
    mdb_path: str, 
    excel_folder: str, 
    log_callback: Optional[Callable[[str], None]] = None
) -> Optional[Any]:
    """
    Initialize comprehensive file path configuration for foundation design automation workflow.
    
    This function establishes the complete file system structure required for foundation design
    automation, including input data paths, output directory creation, and result file organization.
    It creates a standardized directory hierarchy that supports multi-pile type design workflows,
    comprehensive result management, and integration with various analysis tools.
    
    The function creates the following directory structure:
    - Analytical_Results/: Main results container
      - ULS/: Ultimate Limit State analysis results
      - SLS/: Serviceability Limit State analysis results
      - BP_Prokon/: Bored pile Prokon analysis files
      - BP_NM_Design_Detail/: Detailed N-M interaction results
    """
    # Validate input parameters for essential file paths
    if not mdb_path or not excel_folder:
        if log_callback:
            log_callback("════════════════════════════════════════════════════")
            log_callback("❌ INITIALIZATION ERROR - Missing Required Input Files")
            log_callback("════════════════════════════════════════════════════")
            log_callback("ERROR: Both SAFE Result Database and Excel Master Data Folder must be provided.")
            log_callback("   • SAFE Database: Required for structural analysis results")
            log_callback("   • Excel Folder: Required for design parameters and specifications")
        return None

    if log_callback:
        log_callback("════════════════════════════════════════════════════")
        log_callback("🔧 FOUNDATION DESIGN AUTOMATION - SYSTEM INITIALIZATION")
        log_callback("════════════════════════════════════════════════════")
        log_callback("Initializing file system structure for foundation design workflow...")
        log_callback("")

    # Initialize file paths configuration object
    file_paths = main_class.FilePaths()
    
    # Configure SAFE analysis database path for structural force extraction
    file_paths.ResultMdbSAFE = mdb_path
    
    # Configure Excel input file paths for design parameters and specifications
    excel_folder_path = Path(excel_folder)
    file_paths.ExcelProperty = str(excel_folder_path / EXCEL_PROPERTY_FILENAME)    # Material properties and pile specifications
    file_paths.ExcelGeometry = str(excel_folder_path / EXCEL_GEOMETRY_FILENAME)   # Foundation layout and geometric configuration
    file_paths.ExcelGeology = str(excel_folder_path / EXCEL_GEOLOGY_FILENAME)     # Geotechnical soil properties and parameters
    file_paths.ExcelLoading = str(excel_folder_path / EXCEL_LOADING_FILENAME)     # Loading conditions and load combinations    # Create standardized output directory structure for organized result management
    base_path = Path(mdb_path).parent / ANALYTICAL_RESULTS_FOLDER  # Main results container
    path_uls = base_path / ULS_FOLDER                              # Ultimate Limit State results
    path_sls = base_path / SLS_FOLDER                              # Serviceability Limit State results

    # Create directory hierarchy with proper permissions and error handling
    if log_callback:
        log_callback("📁 Creating standardized output directory structure:")
    
    directory_descriptions = {
        base_path: "Main analytical results container",
        path_uls: "Ultimate Limit State (ULS) design verification results", 
        path_sls: "Serviceability Limit State (SLS) performance checks"
    }
    
    for path in [base_path, path_uls, path_sls]:
        path.mkdir(parents=True, exist_ok=True)
        if log_callback:
            description = directory_descriptions.get(path, "Analysis results")
            log_callback(f"   ✓ {path.name}: {description}")
    
    # Configure main output file paths for design results and logging
    file_paths.Log = str(base_path / DESIGN_LOG_FILENAME)
    file_paths.ResultDesignLoad = str(base_path / DESIGN_LOAD_FILENAME)
    file_paths.ResultPileCapacity = str(base_path / PILE_CAPACITY_FILENAME)
    file_paths.ResultPilingSchedule = str(base_path / PILING_SCHEDULE_FILENAME)    # ULS outputs
    file_paths.ExcelOutputPileULS = str(path_uls / PILE_ULS_SQLITE_FILENAME)
    file_paths.ExcelOutputPileSLS = str(path_uls / PILE_SLS_SQLITE_FILENAME)
    file_paths.ExcelOutputPileULSPDelta = str(path_uls / PILE_ULS_PDELTA_SQLITE_FILENAME)
    file_paths.ExcelOutputPileULSPDeltaCal = str(path_uls / PILE_ULS_PDELTA_CAL_SQLITE_FILENAME)  # For P-Delta calculations
    file_paths.ResultSteelHPileCheckBD = str(path_uls / STEEL_HPILE_CHECK_BD_SQLITE_FILENAME)
    file_paths.ResultSteelHPileCheckASD = str(path_uls / STEEL_HPILE_CHECK_ASD_SQLITE_FILENAME)
    file_paths.ResultPileNeedsPDelta = str(path_uls / PILE_NEEDS_PDELTA_FILENAME)
    file_paths.ResultProkonFolder = str(path_uls / BP_PROKON_FOLDER)
    file_paths.ResultBPSegmentNMFolder = str(path_uls / BP_NM_DESIGN_DETAIL_FOLDER)
    file_paths.ResultBPShearCheck = str(path_uls / BP_SHEAR_CHECK_FILENAME)
    file_paths.ResultBPShearDesign = str(path_uls / BP_SHEAR_DESIGN_FILENAME)
    file_paths.ResultBPSegmentRebarLog = str(path_uls / BP_SEGMENT_REBAR_LOG_FILENAME)
    file_paths.ResultBPRebarLog = str(path_uls / BP_REBAR_LOG_FILENAME)
    file_paths.ResultSHPCheck = str(path_uls / SHP_CHECK_FILENAME)
    file_paths.ResultDHPCheck = str(path_uls / DHP_CHECK_FILENAME)    # SLS outputs
    file_paths.ExcelOutputPileLocalXY = str(path_sls / PILE_LOCAL_XY_SQLITE_FILENAME)
    file_paths.ExcelOutputSettlement = str(path_sls / SETTLEMENT_SQLITE_FILENAME)
    file_paths.ExcelOutputLateralDisplacement = str(path_sls / LATERAL_DISPLACEMENT_SQLITE_FILENAME)
    file_paths.ResultPileDeflection = str(path_sls / PILE_DEFLECTION_CHECK_FILENAME)
    file_paths.ResultPileDeflectionFail = str(path_sls / PILE_DEFLECTION_FAIL_FILENAME)
    file_paths.ResultAngularRotation = str(path_sls / ANGULAR_ROTATION_CHECK_FILENAME)
    file_paths.ResultAngularRotationFail = str(path_sls / ANGULAR_ROTATION_FAIL_FILENAME)
    file_paths.ResultDifferentialSettlement = str(path_sls / DIFFERENTIAL_SETTLEMENT_CHECK_FILENAME)
    file_paths.ResultDifferentialSettlementFail = str(path_sls / DIFFERENTIAL_SETTLEMENT_FAIL_FILENAME)

    if log_callback:
        log_callback("")
        log_callback("✅ Foundation design file system structure initialized successfully!")
        log_callback("   Ready for comprehensive foundation analysis and design verification.")
        log_callback("")
    
    return file_paths


def read_input_data(
    file_paths: Any, 
    excel_inputs: Any, 
    safe_mdbs: Any, 
    log_callback: Optional[Callable[[str], None]] = None
) -> None:
    """
    Read and validate comprehensive input data from SAFE database and Excel design files.
    
    This function orchestrates the complete input data acquisition process for foundation design
    automation, including structural analysis results from SAFE software and design parameters
    from Excel-based input files. It performs data validation, consistency checking, and
    initialization of specialized design modules as required.
    
    The function processes the following data categories:
    1. SAFE structural analysis results (forces, displacements, load combinations)
    2. Foundation property specifications (materials, sections, capacities)
    3. Geometric layout and pile configuration data
    4. Geotechnical soil properties and design parameters
    5. Loading conditions and load combination definitions
    6. Steel section properties for H-pile design applications
    """
    if log_callback:
        log_callback("════════════════════════════════════════════════════")
        log_callback("📊 INPUT DATA PROCESSING - Foundation Design Parameters")
        log_callback("════════════════════════════════════════════════════")
        log_callback("")

    # Phase 1: Read SAFE structural analysis database
    # Extract forces, displacements, load combinations, and element definitions
    if log_callback:
        log_callback("🏗️  Phase 1: SAFE Structural Analysis Database Processing")
        log_callback("   • Loading foundation element forces, moments, and displacements")
        log_callback("   • Importing load combinations and design load patterns")
        log_callback("   • Validating structural analysis results and element connectivity")
    _read.read_safe_mdbs(safe_mdbs, file_paths, log_callback)

    # Phase 2: Read Excel-based design input files with validation
    if log_callback:
        log_callback("")
        log_callback("📋 Phase 2: Design Input Data Acquisition")
        log_callback("   • Foundation material properties and pile specifications")
        log_callback("   • Geometric layouts and pile positioning coordinates")  
        log_callback("   • Geotechnical parameters and soil engineering properties")
        log_callback("   • Load patterns, combinations, and design criteria")
    
    # Load foundation property specifications (materials, pile types, capacities)
    if os.path.exists(file_paths.ExcelProperty):
        excel_inputs = read_input_property(excel_inputs, file_paths)
    
    # Load geometric layout and pile configuration data
    if os.path.exists(file_paths.ExcelGeometry):
        excel_inputs = read_input_geometry(excel_inputs, file_paths)
    
    # Load geotechnical soil properties and design parameters
    if os.path.exists(file_paths.ExcelGeology):
        excel_inputs = read_input_geology(excel_inputs, file_paths)
    
    # Load loading conditions and load combination definitions
    if os.path.exists(file_paths.ExcelLoading):
        excel_inputs = read_input_loading(excel_inputs, file_paths)
    
    # Load steel section properties for H-pile design (if available)
    if os.path.exists(file_paths.DataSteel):
        excel_inputs = read_input_steel(excel_inputs, file_paths)

    # Phase 3: Initialize specialized design modules based on pile type requirements
    if log_callback:
        log_callback("")
        log_callback("🔧 Phase 3: Design Module Initialization & Configuration")

    # Initialize Bored Pile (BP) reinforcement design modules if BP piles are present
    if 'BP' in excel_inputs.Pile['Pile Type'].values:
        if log_callback:
            log_callback("   • Bored Pile (BP) reinforcement design system activation")
            log_callback("     - Standard reinforcement patterns and configuration libraries")
            log_callback("     - Segment-based N-M interaction analysis framework")
        # Initialize standard BP reinforcement configuration
        init_excel_bp_rebar(file_paths, excel_inputs)
        # Initialize segment-based BP reinforcement for detailed N-M analysis
        init_excel_bp_segment_rebar(file_paths, excel_inputs, safe_mdbs)


def run_uls_sls_calculations(
    file_paths: Any, 
    safe_mdbs: Any, 
    excel_inputs: Any, 
    excel_outputs: Any, 
    design_results: Any, 
    log_callback: Optional[Callable[[str], None]] = None
) -> None:
    """
    Execute comprehensive Ultimate Limit State (ULS) and Serviceability Limit State (SLS) calculations.
    
    This function orchestrates the complete structural analysis workflow for pile foundation design,
    including both Ultimate Limit State verification under factored loads and Serviceability Limit
    State assessment under working loads. It processes SAFE structural analysis results, performs
    specialized foundation engineering calculations, and generates comprehensive design verification
    data for all pile types.
    
    The function implements the following calculation sequence:
    1. NSF (Near Surface Foundation) data processing and validation
    2. Comprehensive SLS analysis including settlements, displacements, and deflections
    3. ULS and SLS force extraction and processing for design verification
    4. Integration of results for downstream design optimization procedures
    
    Calculation Components:
    - **Settlement Analysis**: Total and differential settlement calculations following
      established geotechnical methods (elastic theory, consolidation analysis)
    - **Lateral Displacement Analysis**: Pile head displacement and rotation calculations
      under lateral loading with soil-structure interaction
    - **Local Deflection Analysis**: Detailed pile deflection analysis along pile length
      considering soil modulus variation and load distribution
    - **Force Processing**: Extraction and organization of ULS/SLS forces from SAFE
      analysis for design verification and capacity calculations
    """
    # Initialize logging helper function for consistent progress reporting
    def log(msg):
        if log_callback:
            log_callback(msg)
    
    log("════════════════════════════════════════════════════")
    log("⚖️  STRUCTURAL ANALYSIS - ULS & SLS Force Calculations")
    log("════════════════════════════════════════════════════")
    log("Computing comprehensive foundation forces for Ultimate & Serviceability Limit States")
    log("")

    # Phase 1: Process Near Surface Foundation (NSF) data from SAFE analysis
    # Validate and process column and brace element forces for foundation design
    if hasattr(safe_mdbs, 'ElementForcesColumnsAndBraces') and isinstance(
            safe_mdbs.ElementForcesColumnsAndBraces, pd.DataFrame):
        log("🔍 Phase 1: Near Surface Foundation (NSF) Data Processing")
        log("   • Extracting superstructure-to-foundation interface forces")
        log("   • Processing column and brace element load transfer data")
        # Process NSF data to extract foundation-relevant forces and moments
        safe_mdbs = process_nsf(safe_mdbs, excel_inputs, log_callback)
    else:
        log("❌ CRITICAL ERROR: SAFE Analysis Data Missing")
        log("   ElementForcesColumnsAndBraces data not found in SAFE database")
        log("   • Verify SAFE analysis execution completed successfully")
        log("   • Check database file integrity and read permissions")
        log("   • Ensure foundation elements are properly defined in SAFE model")
        return

    # Phase 2: Execute comprehensive Serviceability Limit State (SLS) calculations
    # Perform geotechnical analysis for settlement, displacement, and deflection assessment
    log("")
    log("📐 Phase 2: Serviceability Limit State (SLS) Performance Analysis")
    log("   Foundation performance verification under working load conditions:")
    
    sls_calculations = [
        ("   • Computing foundation settlements (total & differential)", cal_settlement),
        ("   • Analyzing lateral pile head displacements and rotations", cal_lateral_displacement), 
        ("   • Evaluating detailed pile deflection profiles along depth", cal_pile_local_deflection)
    ]
    
    # Execute SLS calculations in sequence with progress tracking
    for msg, func in sls_calculations:
        log(msg)
        func(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback)

    # Phase 3: Extract and process ULS and SLS forces for design verification
    # Calculate factored (ULS) and unfactored (SLS) forces for all pile elements
    log("")
    log("💪 Phase 3: Ultimate Limit State (ULS) Force Processing")
    log("   • Computing factored loads with code-specified safety factors")
    log("   • Processing critical load combinations for strength verification")
    
    # Extract Ultimate Limit State forces with appropriate load factors
    cal_pile_uls(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback)
    
    log("")
    log("🔧 Phase 4: Serviceability Limit State (SLS) Force Processing") 
    log("   • Extracting unfactored working loads for serviceability checks")
    log("   • Preparing force datasets for deflection and performance verification")
    
    # Extract Serviceability Limit State forces under working loads
    cal_pile_sls(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback)

    log("")
    log("✅ Structural analysis complete - Foundation forces ready for comprehensive design verification")
    log("")


def run_design_automation(
    file_paths: Any, 
    safe_mdbs: Any, 
    excel_inputs: Any, 
    excel_outputs: Any, 
    design_results: Any,
    use_tgn53: bool = True, 
    log_callback: Optional[Callable[[str], None]] = None
) -> None:
    """
    Execute comprehensive design automation workflow for multiple pile foundation types.
    
    This function orchestrates the complete automated design process for all pile types present
    in the foundation system, including capacity calculations, design verification, and
    serviceability checks. It implements a systematic approach to foundation design following
    established geotechnical and structural engineering principles with support for multiple
    international design codes and methodologies.
    
    The automation workflow includes:
    1. **Common Foundation Analysis**: Calculations applicable to all pile types including
       stepping effects, capacity determination, and schedule generation
    2. **Serviceability Verification**: Comprehensive SLS checks including deflections,
       angular rotations, and differential settlements
    3. **Pile-Specific Design**: Specialized design procedures for each pile type with
       appropriate design codes and verification methods
    
    Supported Pile Types and Design Methods:
    - **Bored Piles (BP)**: Reinforced concrete design with N-M interaction analysis,
      reinforcement optimization, and shear capacity verification
    - **Socket H-Piles (SHP)**: Steel design with advanced N-M-V interaction analysis
      and connection design verification
    - **Driven H-Piles (DHP)**: Driven pile analysis with drivability assessment and
      structural capacity verification
    - **Mini Piles (MP)**: Specialized small-diameter pile design for restricted access
      applications (verification procedures under development)
    """
    def log(msg):
        if log_callback:
            log_callback(msg)
    
    log("════════════════════════════════════════════════════")
    log("🎯 FOUNDATION DESIGN AUTOMATION - Comprehensive Verification")
    log("════════════════════════════════════════════════════")
    
    # Get pile types
    pile_types = set(excel_inputs.Pile['Pile Type'].values)
    pile_type_names = {
        'BP': 'Bored Piles', 'SHP': 'Socket H-Piles', 
        'DHP': 'Driven H-Piles', 'MP': 'Mini Piles'
    }
    detected_types = [pile_type_names.get(pt, pt) for pt in pile_types]
    log(f"Foundation types detected for design verification: {', '.join(detected_types)}")
    log("")
    
    # Run common calculations
    log("📊 Phase 1: Foundation Engineering Analysis & Capacity Assessment")
    common_calculations = [
        ("   • Computing stepping effects for bored pile construction sequencing", lambda: cal_stepping_effect(
            file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback)),
        ("   • Calculating geotechnical pile capacities using established methods", lambda: cal_capacity_piles(
            file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, use_tgn53, log_callback)),
        ("   • Generating comprehensive piling schedule and specifications", lambda: gen_piling_schedule(
            file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback)),
    ]
    
    for msg, func in common_calculations:
        log(msg)
        func()
    
    log("")
    log("🔍 Phase 2: Serviceability Performance Verification & Compliance Checks")
    performance_checks = [
        ("   • Verifying pile deflection compliance (≤25mm serviceability limit)", lambda: check_pile_deflection(
            file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback)),
        ("   • Checking angular rotation compliance (≤1:500 code requirement)", lambda: check_slab_angular_rotation_xy(
            file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback)),
        ("   • Assessing differential settlement compliance with structural tolerances", lambda: check_differential_settlement(
            file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback))
    ]
    
    for msg, func in performance_checks:
        log(msg)
        func()

    # Design specific pile types
    log("")
    log("🔧 Phase 3: Pile-Specific Design Verification & Code Compliance")
    
    pile_design_functions = {
        'SHP': design_shp,
        'DHP': design_dhp,
        'MP': design_mp,
        'BP': design_bp
    }
    
    for pile_type, design_func in pile_design_functions.items():
        if pile_type in pile_types:
            log(f"   • Initiating {pile_type_names.get(pile_type, pile_type)} comprehensive design verification...")
            design_func(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback)


def design_bp(
    file_paths: Any, 
    safe_mdbs: Any, 
    excel_inputs: Any, 
    excel_outputs: Any, 
    design_results: Any, 
    log_callback: Optional[Callable[[str], None]] = None
) -> Any:
    """
    Execute comprehensive Bored Pile (BP) design verification and optimization workflow.
    
    This function implements the complete design process for reinforced concrete bored pile
    foundations, including advanced moment-axial (N-M) interaction analysis, reinforcement
    design optimization, and shear capacity verification. It follows established reinforced
    concrete design principles according to international codes (ACI 318, Eurocode 2, etc.)
    with specialized procedures for drilled shaft foundations.
    
    The BP design workflow includes:
    1. **N-M Interaction Analysis**: Detailed moment-axial interaction curve generation
       for all pile segments with consideration of material nonlinearity
    2. **Reinforcement Optimization**: Automated reinforcement design with multi-layer
       optimization for structural efficiency and constructability
    3. **Result Integration**: Combination of segment-level results into comprehensive
       pile-level design verification reports
    4. **Shear Design**: Comprehensive shear capacity verification and transverse
       reinforcement design following concrete design codes
    5. **External Tool Integration**: Generation of Prokon analysis files for
       independent verification and advanced analysis capabilities
    """
    def log(msg):
        if log_callback:
            log_callback(msg)
    
    if 'BP' not in excel_inputs.Pile['Pile Type'].values:
        log("⚠️  No Bored Piles (BP) detected in foundation layout - skipping BP design")
        return design_results

    log("🔨 BORED PILE (BP) DESIGN VERIFICATION")
    log("   Reinforced concrete pile foundations with advanced N-M interaction analysis")
    log("")
    
    # Run BP design sequence
    design_results = design_all_bp_segment_nm(
        file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback)

    # Combine results
    log("📊 Consolidating N-M Interaction Analysis Results:")
    log("   • Consolidating segment-level interaction curve visualizations")
    combine_segment_pngs_for_pile(file_paths.ResultBPSegmentNMFolder, log_callback)
    
    log("   • Merging detailed calculation spreadsheets and analysis reports")
    combine_segment_excels_for_pile(file_paths.ResultBPSegmentNMFolder, log_callback)

    design_results = group_bp_segment_rebar(
        file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback)

    # Update inputs with design results
    excel_inputs.BPRebar = design_results.BPRebarLog.copy()
    excel_inputs.BPSegmentRebar = design_results.BPSegmentRebarLog.copy()

    # Generate Prokon files and perform shear checks
    log("")
    log("🔍 Final Design Verification & Independent Analysis:")
    log("   • Generating Prokon analysis files for third-party verification")
    gen_all_c13(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback)
    
    log("   • Performing comprehensive shear capacity verification")
    design_results = check_bp_shear(
        file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback)
    design_results = design_bp_shear(design_results, file_paths, log_callback)
    
    log("")
    log("✅ Bored Pile (BP) design verification completed successfully!")
    log("   All reinforcement and structural capacity checks finalized.")
    return design_results


def _design_steel_h_pile(
    file_paths: Any, 
    safe_mdbs: Any, 
    excel_inputs: Any, 
    excel_outputs: Any, 
    design_results: Any,
    pile_type: str, 
    log_callback: Optional[Callable[[str], None]] = None
) -> Any:
    """
    Execute comprehensive steel H-pile design verification for Socket (SHP) and Driven (DHP) piles.
    
    This function implements advanced steel design verification procedures for H-pile foundations
    including Ultimate Limit State (ULS) and Allowable Stress Design (ASD) analysis with
    sophisticated N-M-V (axial-moment-shear) interaction verification. It follows established
    structural steel design principles according to international codes (AISC 360, Eurocode 3)
    with specialized procedures for pile foundation applications.
    
    The steel H-pile design verification includes:
    1. **Combined Stress Analysis**: Verification of combined axial, bending, and shear stresses
       against material capacity limits with appropriate interaction formulations
    2. **Load Combination Processing**: Separate analysis for gravity and wind load combinations
       with appropriate allowable stress factors for temporary loading conditions
    3. **Multi-Directional Analysis**: Verification in both principal directions (X and Y)
       considering biaxial bending and shear effects
    4. **Design Code Compliance**: Comprehensive verification against multiple design standards
       with appropriate safety factors and load combinations
    """
    def log(msg):
        if log_callback:
            log_callback(msg)
    
    pile_name = "Socketed Steel H Piles" if pile_type == 'SHP' else "Driven Steel H Piles"
    pile_symbol = "🔩" if pile_type == 'SHP' else "🔨"
    
    if pile_type not in excel_inputs.Pile['Pile Type'].values:
        log(f"⚠️  No {pile_name} ({pile_type}) detected in foundation layout - skipping design")
        return design_results

    log(f"{pile_symbol} {pile_name.upper()} ({pile_type}) DESIGN VERIFICATION")
    log("   Structural steel foundations with advanced N-M-V interaction analysis")
    log("")
    log("🔍 Performing multi-code design verification:")
    log("   • Ultimate Limit State (ULS) with factored loads")
    log("   • Allowable Stress Design (ASD) for gravity and wind loads")
    log("   • Combined axial, bending, and shear stress analysis")
    
    # Perform checks
    design_results = _check_steel_h(
        file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, 
        pile_type=pile_type, log_callback=log_callback)

    # Check results and provide detailed verification summary
    log("")
    log("📋 DESIGN VERIFICATION SUMMARY:")
    
    check_categories = [
        ("🏗️  Ultimate Limit State (ULS) - Building Code:", [
            ('StressCheckBD', 'Combined Stress Verification', 'ULS Stress Check (BD)'),
            ('VxCheckBD', 'Shear Force X-Direction', 'ULS Vx Check (BD)'), 
            ('VyCheckBD', 'Shear Force Y-Direction', 'ULS Vy Check (BD)')
        ]),
        ("⚖️  Allowable Stress Design (ASD) - Gravity Loads:", [
            ('StressCheckASDG', 'Combined Stress under Gravity', 'ULS Stress Check (ASD Gravity)'),
            ('VxCheckASDG', 'Shear Force X-Direction', 'ULS Vx Check (ASD Gravity)'),
            ('VyCheckASDG', 'Shear Force Y-Direction', 'ULS Vy Check (ASD Gravity)')
        ]),
        ("💨 Allowable Stress Design (ASD) - Wind Loads:", [
            ('StressCheckASDW', 'Combined Stress with Wind', 'ULS Stress Check (ASD Wind)'),
            ('VxCheckASDW', 'Shear Force X-Direction', 'ULS Vx Check (ASD Wind)'),
            ('VyCheckASDW', 'Shear Force Y-Direction', 'ULS Vy Check (ASD Wind)')
        ])
    ]

    all_passed = True
    for category_name, checks in check_categories:
        log(f"   {category_name}")
        for attr_suffix, check_description, column_name in checks:
            attr = f'{pile_type}ULS{attr_suffix}'
            df = getattr(design_results, attr)
            result = df[column_name].eq('OK').all()
            status = "✅ PASS" if result else "❌ FAIL"
            log(f"     • {check_description}: {status}")
            if not result:
                all_passed = False
    
    log("")
    overall_status = "✅ PASSED" if all_passed else "❌ FAILED" 
    log(f"🎯 Overall {pile_name} Design Verification: {overall_status}")
    log("")
    return design_results


def design_shp(
    file_paths: Any, 
    safe_mdbs: Any, 
    excel_inputs: Any, 
    excel_outputs: Any, 
    design_results: Any, 
    log_callback: Optional[Callable[[str], None]] = None
) -> Any:
    """
    Execute Socket H-Pile (SHP) design verification with specialized socket connection analysis.
    
    Socket H-piles are steel H-sections installed in pre-drilled holes with grouted socket
    connections, providing enhanced load transfer and reduced installation vibration compared
    to driven piles. This function implements comprehensive design verification including
    structural steel analysis and socket connection design considerations.
    """
    return _design_steel_h_pile(file_paths, safe_mdbs, excel_inputs, excel_outputs, 
                               design_results, 'SHP', log_callback)


def design_dhp(
    file_paths: Any, 
    safe_mdbs: Any, 
    excel_inputs: Any, 
    excel_outputs: Any, 
    design_results: Any, 
    log_callback: Optional[Callable[[str], None]] = None
) -> Any:
    """
    Execute Driven H-Pile (DHP) design verification with drivability and installation analysis.
    
    Driven H-piles are steel H-sections installed by impact or vibratory driving methods,
    providing efficient installation for suitable soil conditions. This function implements
    comprehensive design verification including structural steel analysis and drivability
    assessment for successful installation.
    """
    return _design_steel_h_pile(file_paths, safe_mdbs, excel_inputs, excel_outputs, 
                               design_results, 'DHP', log_callback)


def design_mp(
    file_paths: Any, 
    safe_mdbs: Any, 
    excel_inputs: Any, 
    excel_outputs: Any, 
    design_results: Any, 
    log_callback: Optional[Callable[[str], None]] = None
) -> Any:
    """
    Execute Mini Pile (MP) design verification for restricted access foundation applications.
    
    Mini piles are small-diameter pile foundations typically used in restricted access
    situations, underpinning applications, or where conventional pile installation is
    not feasible. This function provides placeholder functionality for future implementation
    of comprehensive mini pile design verification procedures.
    """
    if log_callback:
        if 'MP' in excel_inputs.Pile['Pile Type'].values:
            log_callback("⚙️  MINI PILE (MP) DESIGN VERIFICATION")
            log_callback("   Small-diameter piles for restricted access and specialized applications")
            log_callback("")
            log_callback("⚠️  DEVELOPMENT STATUS: Mini Pile design verification is under development")
            log_callback("   • Advanced design procedures being finalized for specialized applications")
            log_callback("   • Comprehensive verification protocols and code compliance in progress")
            log_callback("   • Current analysis scope limited to basic capacity calculations")
        else:
            log_callback("⚠️  No Mini Piles (MP) detected in foundation layout - skipping design")
    return design_results


def delete_previous_results(
    file_paths: Any, 
    log_callback: Optional[Callable[[str], None]] = None
) -> bool:
    """
    Clean and reinitialize the results directory structure for fresh analysis execution.
    
    This function removes all previous analysis results and recreates a clean directory
    structure for new foundation design calculations. It ensures that previous results
    do not interfere with current analysis and provides a fresh environment for
    comprehensive design verification procedures.
    
    The function performs the following operations:
    1. **Directory Validation**: Verify that file paths are properly initialized
    2. **Previous Results Removal**: Complete deletion of existing results directory
    3. **Directory Recreation**: Establishment of clean directory structure
    4. **Structure Verification**: Confirmation of proper directory hierarchy
    
    Directory Structure Created:
    - Analytical_Results/: Main results container
      - ULS/: Ultimate Limit State analysis results
      - SLS/: Serviceability Limit State analysis results
      - (Additional subdirectories created as needed by specific analysis modules)
    """
    import shutil
    
    # Validate file paths configuration before proceeding with cleanup
    if not hasattr(file_paths, 'ResultMdbSAFE') or not file_paths.ResultMdbSAFE:
        if log_callback:
            log_callback("❌ CONFIGURATION ERROR: File paths not properly initialized")
            log_callback("   Cannot proceed with workspace cleanup operation")
            log_callback("   • Verify initialize_file_paths() was executed successfully")
            log_callback("   • Confirm SAFE database path is valid and accessible")
        return False

    if log_callback:
        log_callback("════════════════════════════════════════════════════")
        log_callback("🧹 WORKSPACE CLEANUP - Preparing Fresh Analysis Environment")
        log_callback("════════════════════════════════════════════════════")

    # Determine results directory location based on SAFE database path
    # Use pathlib for robust cross-platform path operations
    results_dir = Path(file_paths.ResultMdbSAFE).parent / 'Analytical_Results'

    # Remove existing results directory completely to ensure clean environment
    if results_dir.exists():
        if log_callback:
            log_callback(f"🗑️  Removing previous analysis results from: {results_dir}")
            log_callback("   Ensuring clean environment for new foundation design calculations")
        try:
            shutil.rmtree(results_dir)  # Complete directory tree removal
        except PermissionError as e:
            if log_callback:
                log_callback(f"❌ Permission Error: Unable to delete previous results")
                log_callback(f"   Technical Details: {e}")
                log_callback("   Solution: Close any open Excel files or applications accessing results directory")
            return False
        except OSError as e:
            if log_callback:
                log_callback(f"❌ File System Error: Unable to delete previous results")
                log_callback(f"   Technical Details: {e}")
            return False

    # Recreate standardized directory structure for organized result management
    if log_callback:
        log_callback("")
        log_callback("📁 Recreating standardized directory structure:")
    
    directory_descriptions = {
        '': 'Main analytical results container',
        'ULS': 'Ultimate Limit State design verification results',
        'SLS': 'Serviceability Limit State performance verification'
    }
    
    # Create main directory and essential subdirectories for ULS and SLS results
    for subdir in ['', 'ULS', 'SLS']:
        target_dir = results_dir / subdir
        try:
            target_dir.mkdir(parents=True, exist_ok=True)
            if log_callback:
                description = directory_descriptions[subdir]
                folder_name = 'Analytical_Results' if subdir == '' else subdir
                log_callback(f"   ✓ {folder_name}: {description}")
        except OSError as e:
            if log_callback:
                log_callback(f"❌ Error creating directory {target_dir}: {e}")
            return False

    if log_callback:
        log_callback("")
        log_callback("✅ Workspace cleanup completed - Ready for fresh foundation analysis!")
        log_callback("")

    return True

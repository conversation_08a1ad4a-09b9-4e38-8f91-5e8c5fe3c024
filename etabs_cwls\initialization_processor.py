"""
Initialization Processor Module for ETABS CWLS

This module handles the initialization of design parameters and creation of
base Excel templates for the ETABS Core Wall Loading Schedule system. It
processes load cases, story data, and creates the foundational data structures.

The module implements professional programming standards with:
- Enhanced logging system with external file storage
- Zero fallback policy with explicit error handling
- Comprehensive type safety and validation
- Professional initialization patterns

Key Functions:
    initialization: Initialize design parameters and create base Excel template
    read_load_cases: Read load cases from ETABS files
    read_story_data: Read story data from ETABS database
    create_load_mapping_template: Create load case mapping template
    write_excel_template: Write initial Excel template

Version: 5.6.9
Author: Foundation Automation Team
Copyright: © 2023-2025 Foundation Automation. All rights reserved.
"""

import pandas as pd
import time
import os
from typing import Optional, Callable, Tuple, List

from . import _class
from ._read import read_mdbs, read_excel
from ._write import write_excel
from .exceptions import DataValidationError, ETABSConnectionError, FileOperationError
from .logging_config import (
    enhanced_log, log_function_entry, log_function_exit,
    log_validation_result, log_calculation_result, log_error_with_context,
    create_timed_logger, log_performance_metric
)


def initialization(
    file_path: _class.file_path, 
    design_data: _class.design_data, 
    ETABS_converter: _class.ETABS_converter,
    log_callback: Optional[Callable] = None
) -> Tuple[_class.file_path, _class.design_data]:
    """
    Initialize design parameters and create base Excel template.
    
    This function initializes the design data structure by reading load cases
    from ETABS files, setting up story data, and creating the base Excel
    template with parameter sheets, load mapping, and story information.
    
    Args:
        file_path: Configured file path object
        design_data: Design data object to initialize
        ETABS_converter: Configured ETABS converter object
        log_callback: Optional logging callback function
        
    Returns:
        Tuple of (configured file_path, initialized design_data)
        
    Raises:
        DataValidationError: If input parameters are invalid
        ETABSConnectionError: If ETABS database access fails
        FileOperationError: If Excel file operations fail
        
    Example:
        >>> file_path, design_data = initialization(file_path, design_data, converter)
    """
    log_function_entry(log_callback, "initialization", 
                      etabs_version=design_data.etabsversion,
                      has_accessfile2=bool(file_path.accessfile2))
    
    try:
        # Input validation
        if not isinstance(file_path, _class.file_path):
            raise DataValidationError(
                f"file_path must be a file_path instance, got {type(file_path).__name__}",
                field_name="file_path",
                invalid_value=type(file_path).__name__,
                expected_type="file_path",
                error_code="INVALID_FILE_PATH_TYPE"
            )
        
        if not isinstance(design_data, _class.design_data):
            raise DataValidationError(
                f"design_data must be a design_data instance, got {type(design_data).__name__}",
                field_name="design_data",
                invalid_value=type(design_data).__name__,
                expected_type="design_data",
                error_code="INVALID_DESIGN_DATA_TYPE"
            )
        
        enhanced_log(log_callback, "Starting design initialization", 'INFO')
        
        with create_timed_logger(log_callback, "design_initialization") as timer:
            # Initialize parameter dataframe
            design_data.df_parameter = pd.DataFrame(columns=['Global Rotation', 'LoadCase'])
            enhanced_log(log_callback, "Parameter DataFrame initialized", 'DEBUG')
            
            # Read load cases based on ETABS version and configuration
            load_cases_df = read_load_cases(file_path, design_data, ETABS_converter, log_callback)
            design_data.df_parameter['LoadCase'] = load_cases_df['LoadCase']
            
            log_calculation_result(log_callback, "load_cases_found", len(load_cases_df), "cases")
            
            # Read story data
            story_data = read_story_data(file_path, ETABS_converter, log_callback)
            design_data.df_storydata = story_data
            
            log_calculation_result(log_callback, "stories_found", len(story_data), "stories")
            
            # Set up default parameters
            design_data.df_parameter.loc[0, 'Global Rotation'] = 0
            enhanced_log(log_callback, "Default global rotation set to 0", 'DEBUG')
            
            # Create load case mapping template
            load_mapping = create_load_mapping_template(log_callback)
            design_data.df_mapping = load_mapping
            
            log_calculation_result(log_callback, "load_mapping_cases", len(load_mapping), "cases")
            
            # Write initial Excel template
            write_excel_template(file_path, design_data, log_callback)
        
        enhanced_log(log_callback, "Design initialization completed successfully", 'INFO')
        log_validation_result(log_callback, "design_initialization", True,
                            "Successfully initialized design parameters and Excel template")
        log_function_exit(log_callback, "initialization", "Success")
        
        return (file_path, design_data)
        
    except Exception as e:
        log_error_with_context(log_callback, e, "initialization")
        log_validation_result(log_callback, "design_initialization", False, str(e))
        raise


def read_load_cases(
    file_path: _class.file_path,
    design_data: _class.design_data,
    ETABS_converter: _class.ETABS_converter,
    log_callback: Optional[Callable] = None
) -> pd.DataFrame:
    """
    Read load cases from ETABS files based on version and configuration.
    
    Args:
        file_path: File path object
        design_data: Design data object
        ETABS_converter: ETABS converter object
        log_callback: Optional logging callback function
        
    Returns:
        DataFrame containing unique load cases
        
    Raises:
        ETABSConnectionError: If database access fails
    """
    log_function_entry(log_callback, "read_load_cases", etabs_version=design_data.etabsversion)
    
    try:
        all_load_cases = []
        
        # For ETABS 2021, only process Pier Forces
        if design_data.etabsversion == 2:
            enhanced_log(log_callback, "Reading load cases for ETABS 2021 (pier forces only)", 'INFO')
            
            # Read from primary file
            try:
                table_name = ETABS_converter.TN_PierForce
                enhanced_log(log_callback, f"Reading table: {table_name}", 'DEBUG')
                
                g_etabs_data = read_mdbs(file_path.accessfile1, table_name, log_callback)
                g_load_cases = g_etabs_data[ETABS_converter.CN_CaseCombo].unique()
                all_load_cases.extend(g_load_cases)
                
                log_calculation_result(log_callback, "primary_file_load_cases", len(g_load_cases), "cases")
                
            except Exception as e:
                raise ETABSConnectionError(
                    f"Failed to read pier forces from primary file: {str(e)}",
                    database_path=file_path.accessfile1,
                    table_name=table_name,
                    etabs_version="2021",
                    error_code="PIER_FORCE_READ_FAILED"
                ) from e
            
            # Read from secondary file if available
            if file_path.accessfile2:
                try:
                    enhanced_log(log_callback, "Reading from secondary file", 'DEBUG')
                    w_etabs_data = read_mdbs(file_path.accessfile2, table_name, log_callback)
                    w_load_cases = w_etabs_data[ETABS_converter.CN_CaseCombo].unique()
                    all_load_cases.extend(w_load_cases)
                    
                    log_calculation_result(log_callback, "secondary_file_load_cases", len(w_load_cases), "cases")
                    
                except Exception as e:
                    raise ETABSConnectionError(
                        f"Failed to read pier forces from secondary file: {str(e)}",
                        database_path=file_path.accessfile2,
                        table_name=table_name,
                        etabs_version="2021",
                        error_code="PIER_FORCE_READ_FAILED"
                    ) from e
        
        # For ETABS 2016/2017, process based on configuration
        else:
            enhanced_log(log_callback, "Reading load cases for ETABS 2016/2017", 'INFO')
            
            # Check if joint reactions should be used
            if design_data.use_joint_reactions:
                enhanced_log(log_callback, "Using joint reactions for load cases", 'DEBUG')
                
                try:
                    table_name = 'Joint Reactions'
                    design_data.df_jtreaction = read_mdbs(file_path.accessfile1, table_name, log_callback)
                    jt_load_cases = design_data.df_jtreaction['CaseCombo'].unique()
                    all_load_cases.extend(jt_load_cases)
                    
                    log_calculation_result(log_callback, "joint_reaction_load_cases", len(jt_load_cases), "cases")
                    
                except Exception as e:
                    raise ETABSConnectionError(
                        f"Failed to read joint reactions: {str(e)}",
                        database_path=file_path.accessfile1,
                        table_name=table_name,
                        etabs_version="2016/2017",
                        error_code="JOINT_REACTION_READ_FAILED"
                    ) from e
            
            # Use pier forces
            else:
                enhanced_log(log_callback, "Using pier forces for load cases", 'DEBUG')
                
                # Read from primary file
                try:
                    table_name = ETABS_converter.TN_PierForce
                    g_etabs_data = read_mdbs(file_path.accessfile1, table_name, log_callback)
                    g_load_cases = g_etabs_data[ETABS_converter.CN_CaseCombo].unique()
                    all_load_cases.extend(g_load_cases)
                    
                    log_calculation_result(log_callback, "primary_file_load_cases", len(g_load_cases), "cases")
                    
                except Exception as e:
                    raise ETABSConnectionError(
                        f"Failed to read pier forces from primary file: {str(e)}",
                        database_path=file_path.accessfile1,
                        table_name=table_name,
                        etabs_version="2016/2017",
                        error_code="PIER_FORCE_READ_FAILED"
                    ) from e
                
                # Read from secondary file if available
                if file_path.accessfile2:
                    try:
                        w_etabs_data = read_mdbs(file_path.accessfile2, table_name, log_callback)
                        w_load_cases = w_etabs_data[ETABS_converter.CN_CaseCombo].unique()
                        all_load_cases.extend(w_load_cases)
                        
                        log_calculation_result(log_callback, "secondary_file_load_cases", len(w_load_cases), "cases")
                        
                    except Exception as e:
                        raise ETABSConnectionError(
                            f"Failed to read pier forces from secondary file: {str(e)}",
                            database_path=file_path.accessfile2,
                            table_name=table_name,
                            etabs_version="2016/2017",
                            error_code="PIER_FORCE_READ_FAILED"
                        ) from e
        
        # Create DataFrame with unique load cases
        unique_load_cases = list(set(all_load_cases))
        load_cases_df = pd.DataFrame(unique_load_cases, columns=['LoadCase'])
        
        log_calculation_result(log_callback, "unique_load_cases", len(unique_load_cases), "cases")
        enhanced_log(log_callback, f"Found load cases: {', '.join(unique_load_cases[:10])}{'...' if len(unique_load_cases) > 10 else ''}", 'DEBUG')
        
        log_function_exit(log_callback, "read_load_cases", f"{len(unique_load_cases)} cases")
        return load_cases_df

    except Exception as e:
        log_error_with_context(log_callback, e, "read_load_cases")
        raise


def read_story_data(
    file_path: _class.file_path,
    ETABS_converter: _class.ETABS_converter,
    log_callback: Optional[Callable] = None
) -> pd.DataFrame:
    """
    Read story data from ETABS database.

    Args:
        file_path: File path object
        ETABS_converter: ETABS converter object
        log_callback: Optional logging callback function

    Returns:
        DataFrame containing story information with elevations

    Raises:
        ETABSConnectionError: If database access fails
    """
    log_function_entry(log_callback, "read_story_data")

    try:
        table_name = ETABS_converter.TN_StoryData
        enhanced_log(log_callback, f"Reading story data from table: {table_name}", 'DEBUG')

        story_data = read_mdbs(file_path.accessfile1, table_name, log_callback)

        # Extract story names and calculate elevations
        story_df = story_data[[ETABS_converter.CN_StoryName]].copy()
        story_df['Elevation'] = story_df.shape[0] - story_df.index

        enhanced_log(log_callback, f"Processed {len(story_df)} stories with elevation data", 'DEBUG')
        log_function_exit(log_callback, "read_story_data", f"{len(story_df)} stories")

        return story_df

    except Exception as e:
        raise ETABSConnectionError(
            f"Failed to read story data: {str(e)}",
            database_path=file_path.accessfile1,
            table_name=table_name,
            error_code="STORY_DATA_READ_FAILED"
        ) from e


def create_load_mapping_template(log_callback: Optional[Callable] = None) -> pd.DataFrame:
    """
    Create load case mapping template with standard load cases.

    Args:
        log_callback: Optional logging callback function

    Returns:
        DataFrame containing load case mapping template
    """
    log_function_entry(log_callback, "create_load_mapping_template")

    try:
        # Standard load cases for structural engineering
        load_cases = [
            'DL', 'SDL', 'LL', 'SL', 'Eqr', 'Uplift',
            'W1', 'W2', 'W3', 'W4', 'W5', 'W6', 'W7', 'W8',
            'W9', 'W10', 'W11', 'W12', 'W13', 'W14', 'W15',
            'W16', 'W17', 'W18', 'W19', 'W20', 'W21', 'W22',
            'W23', 'W24'
        ]

        mapping_df = pd.DataFrame({
            'Load Case': load_cases,
            'Load Name(Gravity)': '',
            'Load Name(Wind)': ''
        })

        enhanced_log(log_callback, f"Created load mapping template with {len(load_cases)} load cases", 'DEBUG')
        log_function_exit(log_callback, "create_load_mapping_template", f"{len(load_cases)} cases")

        return mapping_df

    except Exception as e:
        log_error_with_context(log_callback, e, "create_load_mapping_template")
        raise


def write_excel_template(
    file_path: _class.file_path,
    design_data: _class.design_data,
    log_callback: Optional[Callable] = None
) -> None:
    """
    Write initial Excel template with parameter, mapping, and story sheets.

    Args:
        file_path: File path object
        design_data: Design data object
        log_callback: Optional logging callback function

    Raises:
        FileOperationError: If Excel write operations fail
    """
    log_function_entry(log_callback, "write_excel_template")

    try:
        enhanced_log(log_callback, f"Writing Excel template to: {file_path.design_excel_path}", 'INFO')

        with create_timed_logger(log_callback, "excel_template_write") as timer:
            # Attempt to write all sheets
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    # Write parameter sheet
                    write_excel(design_data.df_parameter, file_path.design_excel_path,
                              file_path.parameter, log_callback)
                    enhanced_log(log_callback, "Parameter sheet written", 'DEBUG')

                    # Write mapping sheet
                    write_excel(design_data.df_mapping, file_path.design_excel_path,
                              file_path.mapping, log_callback)
                    enhanced_log(log_callback, "Mapping sheet written", 'DEBUG')

                    # Write story sheet
                    write_excel(design_data.df_storydata, file_path.design_excel_path,
                              file_path.storylist, log_callback)
                    enhanced_log(log_callback, "Story sheet written", 'DEBUG')

                    break  # Success - exit retry loop

                except PermissionError as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        raise FileOperationError(
                            f"Cannot write Excel file after {max_retries} attempts. "
                            f"Please close the Excel file if it's open: {file_path.design_excel_path}",
                            file_path=file_path.design_excel_path,
                            operation="write",
                            error_code="EXCEL_FILE_LOCKED"
                        ) from e

                    enhanced_log(log_callback, f"Excel file locked, retry {retry_count}/{max_retries}", 'WARNING')
                    time.sleep(2)  # Wait before retry

                except Exception as e:
                    raise FileOperationError(
                        f"Failed to write Excel template: {str(e)}",
                        file_path=file_path.design_excel_path,
                        operation="write",
                        error_code="EXCEL_TEMPLATE_WRITE_FAILED"
                    ) from e

        # Verify file was created
        if os.path.exists(file_path.design_excel_path):
            file_size = os.path.getsize(file_path.design_excel_path)
            log_performance_metric(log_callback, "template_file_size", file_size, "bytes")
            enhanced_log(log_callback, f"Excel template created successfully ({file_size} bytes)", 'INFO')
        else:
            raise FileOperationError(
                "Excel template file was not created",
                file_path=file_path.design_excel_path,
                operation="verify",
                error_code="EXCEL_TEMPLATE_NOT_CREATED"
            )

        log_validation_result(log_callback, "excel_template_write", True,
                            "Successfully wrote Excel template with 3 sheets")
        log_function_exit(log_callback, "write_excel_template", "Success")

    except Exception as e:
        log_error_with_context(log_callback, e, "write_excel_template")
        log_validation_result(log_callback, "excel_template_write", False, str(e))
        raise


# Export all functions
__all__ = [
    'initialization',
    'read_load_cases',
    'read_story_data',
    'create_load_mapping_template',
    'write_excel_template'
]

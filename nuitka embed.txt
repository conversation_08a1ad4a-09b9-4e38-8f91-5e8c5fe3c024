nuitka --mingw64 --standalone --onefile --enable-plugin=tk-inter --windows-disable-console --windows-icon-from-ico=AIS.ico main.py

nuitka --mingw64 --standalone --enable-plugin=tk-inter --windows-disable-console --windows-icon-from-ico=AIS.ico --include-data-dir=C:/Users/<USER>/PycharmProjects/Foundation-Automation/Library_Steel=Library_Steel --include-data-files=C:/Users/<USER>/PycharmProjects/Foundation-Automation/AIS.ico=AIS.ico --windows-company-name="<PERSON>ze" --windows-file-description="Foundation Design RPA" --windows-product-version="4.7" main.py

nuitka --mingw64 --standalone --enable-plugin=tk-inter --windows-disable-console --windows-icon-from-ico=AIS.ico --include-data-dir=C:/Users/<USER>/PycharmProjects/Foundation-Automation/Library_Steel=Library_Steel --include-data-files=C:/Users/<USER>/PycharmProjects/Foundation-Automation/AIS.ico=AIS.ico --windows-company-name="<PERSON> Sze" --windows-file-description="Foundation Design RPA" --windows-product-version="4.8" main.py
"""
Pile-specific plotting functions for DXF visualization.

This module contains all functions related to plotting piles, pile groups,
pile caps, and pile-related centroids.
"""

from typing import List, Dict, Any, Optional
import math
from ezdxf.enums import TextEntityAlignment

from ..data_types import Point2D, PileGroupResult, PileType
from .text_manager import TextPlacementManager
from .utils import safe_get
from .base_plotters import add_cross_marker, DEFAULT_EDGE_DISTANCE


def plot_group_dxf(msp, group_id: str, data: PileGroupResult, pile_diameter: Optional[float], 
                   color_idx: Optional[int], pc_label: str, text_manager: TextPlacementManager, 
                   log_callback=None) -> None:
    """
    Plot a single pile group with pile cap and pile locations.
    
    Args:
        msp: DXF model space
        group_id: Unique identifier for the pile group
        data: PileGroupResult containing group data
        pile_diameter: Diameter of piles (optional)
        color_idx: Color index for visualization (optional)
        pc_label: Pile cap label
        text_manager: TextPlacementManager for label positioning
        log_callback: Optional logging callback
    """
    try:
        # Get pile locations first as they're needed for final pile cap
        pile_locations = safe_get(data, 'pile_locations', [])
        
        if not pile_locations:
            if log_callback:
                log_callback(f"    No pile locations found for group {group_id}")
            return
        
        # Validate and process pile locations
        valid_pile_locations = _validate_pile_locations(pile_locations, group_id, log_callback)
        
        if not valid_pile_locations:
            if log_callback:
                log_callback(f"    No valid pile locations for group {group_id}")
            return
        
        # Create pile cap
        _create_pile_cap(msp, group_id, data, valid_pile_locations, pc_label, text_manager, log_callback)
        
        # Plot individual piles
        _plot_individual_piles(msp, valid_pile_locations, data, pile_diameter, text_manager, log_callback)
        
        # Plot centroids and other elements
        _plot_group_centroids(msp, group_id, data, valid_pile_locations, text_manager, log_callback)
        
    except Exception as e:
        if log_callback:
            log_callback(f"    Error in plot_group_dxf for {group_id}: {e}")
        raise


def _validate_pile_locations(pile_locations: List, group_id: str, log_callback=None) -> List[Point2D]:
    """Validate pile locations and return valid ones."""
    valid_pile_locations = []
    
    for i, loc in enumerate(pile_locations):
        if isinstance(loc, (list, tuple)) and len(loc) >= 2:
            try:
                x, y = float(loc[0]), float(loc[1])
                if abs(x) < float('inf') and abs(y) < float('inf'):
                    valid_pile_locations.append((x, y))
            except (ValueError, TypeError):
                if log_callback:
                    log_callback(f"    Warning: Invalid pile location at index {i} for group {group_id}")
        else:
            if log_callback:
                log_callback(f"    Warning: Invalid pile location format at index {i} for group {group_id}")
    
    return valid_pile_locations


def _create_pile_cap(msp, group_id: str, data: PileGroupResult, pile_locations: List[Point2D], 
                     pc_label: str, text_manager: TextPlacementManager, log_callback=None) -> None:
    """Create pile cap for the group."""
    try:
        if len(pile_locations) < 3:
            _create_specialized_pile_cap(msp, group_id, pile_locations, pc_label, text_manager, log_callback)
            return
        
        # Create final pile cap using convex hull method
        group_elements = safe_get(data, 'elements', {})
        preselection_analysis = safe_get(data, 'preselection_analysis', {})
        edge_distance = preselection_analysis.get('edge_distance', DEFAULT_EDGE_DISTANCE) if preselection_analysis else DEFAULT_EDGE_DISTANCE
        
        from ..pile_cap_geometry.pile_cap_geometry import create_pile_cap_for_piles_and_structures
        final_cap_result = create_pile_cap_for_piles_and_structures(
            group_elements=group_elements,
            pile_locations=pile_locations,
            edge_dist=edge_distance
        )
        
        if not final_cap_result['is_valid'] or final_cap_result['polygon'].is_empty:
            raise ValueError(f"Failed to create valid final pile cap for group {group_id}")
        
        final_pile_cap = final_cap_result['polygon']
        coords = list(final_pile_cap.exterior.coords)
        
        if len(coords) < 4:
            raise ValueError(f"Insufficient coordinates for final pile cap: {len(coords)}")
        
        # Create the polyline for final pile cap
        polyline = msp.add_lwpolyline(coords[:-1])
        polyline.close()
        polyline.dxf.layer = 'PILE_CAPS'
        
        # Add pile cap label at centroid
        centroid = final_pile_cap.centroid
        final_x, final_y = text_manager.add_text_position(centroid.x, centroid.y, pc_label, 0.4)
        
        text = msp.add_text(pc_label, dxfattribs={'layer': 'TEXT', 'height': 0.4})
        text.set_placement((final_x, final_y), align=TextEntityAlignment.MIDDLE_CENTER)
        
        if log_callback:
            log_callback(f"    Final pile cap created with {len(coords)-1} vertices for group {group_id}")
            
    except Exception as cap_error:
        if log_callback:
            log_callback(f"    Failed to create pile cap for group {group_id}: {cap_error}")


def _create_specialized_pile_cap(msp, group_id: str, pile_locations: List[Point2D], 
                                pc_label: str, text_manager: TextPlacementManager, log_callback=None) -> None:
    """Create specialized pile caps for 1-2 piles."""
    try:
        if len(pile_locations) == 1:
            _create_single_pile_cap(msp, pile_locations[0], pc_label, text_manager, log_callback)
        elif len(pile_locations) == 2:
            _create_two_pile_cap(msp, pile_locations, pc_label, text_manager, log_callback)
            
        if log_callback:
            log_callback(f"    Created specialized pile cap for group {group_id} with {len(pile_locations)} piles")
            
    except Exception as e:
        if log_callback:
            log_callback(f"    Failed to create specialized pile cap for group {group_id}: {e}")


def _create_single_pile_cap(msp, pile_location: Point2D, pc_label: str, 
                           text_manager: TextPlacementManager, log_callback=None) -> None:
    """Create circular pile cap for single pile."""
    pile_x, pile_y = pile_location
    edge_distance = DEFAULT_EDGE_DISTANCE
    radius = edge_distance + 0.5  # Minimum cap size

    # Create circle as polygon approximation
    num_points = 16
    circle_coords = []
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = pile_x + radius * math.cos(angle)
        y = pile_y + radius * math.sin(angle)
        circle_coords.append((x, y))

    polyline = msp.add_lwpolyline(circle_coords)
    polyline.close()
    polyline.dxf.layer = 'PILE_CAPS'

    # Add label
    final_x, final_y = text_manager.add_text_position(pile_x, pile_y, pc_label, 0.4)
    text = msp.add_text(pc_label, dxfattribs={'layer': 'TEXT', 'height': 0.4})
    text.set_placement((final_x, final_y), align=TextEntityAlignment.MIDDLE_CENTER)


def _create_two_pile_cap(msp, pile_locations: List[Point2D], pc_label: str, 
                        text_manager: TextPlacementManager, log_callback=None) -> None:
    """Create rectangular pile cap for two piles."""
    pile1_x, pile1_y = pile_locations[0]
    pile2_x, pile2_y = pile_locations[1]
    edge_distance = DEFAULT_EDGE_DISTANCE

    # Calculate rectangle around two piles
    min_x = min(pile1_x, pile2_x) - edge_distance
    max_x = max(pile1_x, pile2_x) + edge_distance
    min_y = min(pile1_y, pile2_y) - edge_distance
    max_y = max(pile1_y, pile2_y) + edge_distance

    # Ensure minimum cap dimensions
    width = max_x - min_x
    height = max_y - min_y
    if width < 1.0:  # Minimum 1m width
        center_x = (min_x + max_x) / 2
        min_x = center_x - 0.5
        max_x = center_x + 0.5
    if height < 1.0:  # Minimum 1m height
        center_y = (min_y + max_y) / 2
        min_y = center_y - 0.5
        max_y = center_y + 0.5

    rect_coords = [(min_x, min_y), (max_x, min_y), (max_x, max_y), (min_x, max_y)]

    polyline = msp.add_lwpolyline(rect_coords)
    polyline.close()
    polyline.dxf.layer = 'PILE_CAPS'

    # Add label at center
    center_x = (min_x + max_x) / 2
    center_y = (min_y + max_y) / 2
    final_x, final_y = text_manager.add_text_position(center_x, center_y, pc_label, 0.4)
    text = msp.add_text(pc_label, dxfattribs={'layer': 'TEXT', 'height': 0.4})
    text.set_placement((final_x, final_y), align=TextEntityAlignment.MIDDLE_CENTER)


def _plot_individual_piles(msp, pile_locations: List[Point2D], data: PileGroupResult,
                          pile_diameter: Optional[float], text_manager: TextPlacementManager,
                          log_callback=None) -> None:
    """Plot individual pile locations."""
    if log_callback:
        log_callback(f"    Processing {len(pile_locations)} pile locations")

    successfully_plotted_piles = 0
    pile_plotting_errors = []

    # Get pile specification if available
    pile_spec = safe_get(data, 'selected_pile_spec') or safe_get(data, 'pile_spec')

    for i, pile_pos in enumerate(pile_locations):
        try:
            x, y = float(pile_pos[0]), float(pile_pos[1])

            # Validate coordinates are reasonable
            if abs(x) > 10000 or abs(y) > 10000:
                error_msg = f"Pile {i+1} has unreasonable coordinates: ({x}, {y})"
                pile_plotting_errors.append(error_msg)
                continue

            # Use enhanced pile visualization if pile spec is available
            if pile_spec:
                success = plot_enhanced_pile_dxf(msp, x, y, pile_spec, i+1, text_manager, log_callback)
                if success:
                    successfully_plotted_piles += 1
                else:
                    pile_plotting_errors.append(f"Enhanced pile plotting failed for pile {i+1}")
            else:
                # Use traditional circle visualization
                radius = (pile_diameter or 0.6) / 2.0
                circle = msp.add_circle((x, y), radius)
                circle.dxf.layer = 'PILES_BP'  # Default to BP layer

                # Add pile number text with overlap prevention
                pile_label = f"P{i+1}"
                label_x = x + radius + 0.1
                final_label_x, final_label_y = text_manager.add_text_position(label_x, y, pile_label, 0.3)

                text = msp.add_text(pile_label, dxfattribs={'layer': 'TEXT', 'height': 0.3})
                text.set_placement((final_label_x, final_label_y), align=TextEntityAlignment.LEFT)
                successfully_plotted_piles += 1

        except Exception as pile_error:
            error_msg = f"Error plotting pile {i+1}: {pile_error}"
            pile_plotting_errors.append(error_msg)

    if log_callback:
        log_callback(f"    Successfully plotted {successfully_plotted_piles}/{len(pile_locations)} piles")
        if pile_plotting_errors:
            log_callback(f"    Pile plotting errors ({len(pile_plotting_errors)}):")
            for error in pile_plotting_errors[:3]:  # Show first 3 errors
                log_callback(f"      - {error}")
            if len(pile_plotting_errors) > 3:
                log_callback(f"      ... and {len(pile_plotting_errors) - 3} more errors")


def _plot_group_centroids(msp, group_id: str, data: PileGroupResult, pile_locations: List[Point2D],
                         text_manager: TextPlacementManager, log_callback=None) -> None:
    """Plot centroids for the group."""
    try:
        # Plot main load centroid
        load_centroid = safe_get(data, 'load_centroid')
        if load_centroid and len(load_centroid) >= 2:
            plot_load_centroid_dxf(msp, group_id, load_centroid, text_manager, label_prefix="LC", log_callback=log_callback)

        # Plot sub-cluster load centroids
        sub_clusters = safe_get(data, 'sub_clusters', {})
        if sub_clusters:
            plot_sub_cluster_centroids_dxf(msp, group_id, sub_clusters, text_manager, log_callback=log_callback)

        # Plot pile centroid
        if pile_locations:
            plot_pile_centroid_dxf(msp, data, pile_locations, text_manager, log_callback=log_callback)

    except Exception as e:
        if log_callback:
            log_callback(f"    Error plotting centroids for {group_id}: {e}")


def plot_load_centroid_dxf(msp, group_id: str, load_centroid: Point2D, text_manager: TextPlacementManager,
                           label_prefix: str = "LC", log_callback=None) -> None:
    """
    Plot load centroid with cross marker and label.

    Args:
        msp: DXF model space
        group_id: Group identifier
        load_centroid: Load centroid coordinates
        text_manager: TextPlacementManager for label positioning
        label_prefix: Prefix for the label (default: "LC")
        log_callback: Optional logging callback
    """
    try:
        x, y = float(load_centroid[0]), float(load_centroid[1])

        # Add cross marker
        add_cross_marker(msp, x, y, 0.5, 'LOAD_CENTROIDS')

        # Add label with overlap prevention
        label = f"{label_prefix}-{group_id}"
        final_x, final_y = text_manager.add_text_position(x + 0.3, y + 0.3, label, 0.4)

        text = msp.add_text(label, dxfattribs={'layer': 'TEXT', 'height': 0.4})
        text.set_placement((final_x, final_y), align=TextEntityAlignment.LEFT)

    except Exception as e:
        if log_callback:
            log_callback(f"Error plotting load centroid for {group_id}: {e}")


def plot_sub_cluster_centroids_dxf(msp, group_id: str, sub_clusters: Dict[str, Any],
                                   text_manager: TextPlacementManager, log_callback=None) -> None:
    """
    Plot sub-cluster load centroids with markers and labels.

    Args:
        msp: DXF model space
        group_id: Group identifier
        sub_clusters: Dictionary of sub-cluster data
        text_manager: TextPlacementManager for label positioning
        log_callback: Optional logging callback
    """
    try:
        for sub_cluster_name, sub_cluster_data in sub_clusters.items():
            # Calculate centroid from load points
            load_points = sub_cluster_data.get('load_points', [])
            if not load_points:
                continue

            # Calculate weighted centroid based on load points
            total_load = 0
            weighted_x = 0
            weighted_y = 0

            for x, y, load in load_points:
                weighted_x += float(x) * float(load)
                weighted_y += float(y) * float(load)
                total_load += float(load)

            if total_load > 0:
                centroid_x = weighted_x / total_load
                centroid_y = weighted_y / total_load

                # Add smaller cross marker for sub-cluster centroid
                add_cross_marker(msp, centroid_x, centroid_y, 0.3, 'SUB_CLUSTER_CENTROIDS')

                # Add sub-cluster label with overlap prevention
                sub_cluster_id = sub_cluster_name.replace('sub_cluster_', 'SC')
                label = f"{sub_cluster_id}-{group_id}"
                final_x, final_y = text_manager.add_text_position(centroid_x + 0.2, centroid_y + 0.2, label, 0.3)

                text = msp.add_text(
                    label,
                    dxfattribs={
                        'layer': 'TEXT',
                        'height': 0.3,
                        'color': 5  # Blue color for sub-cluster labels
                    }
                )
                text.set_placement((final_x, final_y), align=TextEntityAlignment.LEFT)

                # Add load value text
                load_value_label = f"{total_load:.0f}kN"
                load_final_x, load_final_y = text_manager.add_text_position(centroid_x - 0.2, centroid_y - 0.2, load_value_label, 0.25)

                load_text = msp.add_text(
                    load_value_label,
                    dxfattribs={
                        'layer': 'TEXT',
                        'height': 0.25,
                        'color': 5  # Blue color for load values
                    }
                )
                load_text.set_placement((load_final_x, load_final_y), align=TextEntityAlignment.RIGHT)

    except Exception as e:
        if log_callback:
            log_callback(f"Error plotting sub-cluster centroids for {group_id}: {e}")


def plot_pile_centroid_dxf(msp, data: PileGroupResult, pile_locations: List[Point2D],
                           text_manager: TextPlacementManager, log_callback=None) -> None:
    """
    Plot pile centroid with marker and offset from load centroid.

    Args:
        msp: DXF model space
        data: PileGroupResult containing group data
        pile_locations: List of pile coordinates
        text_manager: TextPlacementManager for label positioning
        log_callback: Optional logging callback
    """
    try:
        if not pile_locations:
            return

        # Calculate pile centroid
        pile_x = sum(float(pos[0]) for pos in pile_locations) / len(pile_locations)
        pile_y = sum(float(pos[1]) for pos in pile_locations) / len(pile_locations)

        # Add cross marker
        add_cross_marker(msp, pile_x, pile_y, 0.4, 'PILE_CENTROIDS')

        # Calculate offset from load centroid if available
        load_centroid = safe_get(data, 'load_centroid')
        if load_centroid and len(load_centroid) >= 2:
            load_x, load_y = float(load_centroid[0]), float(load_centroid[1])
            offset = math.sqrt((pile_x - load_x)**2 + (pile_y - load_y)**2)

            # Add offset line
            msp.add_line((load_x, load_y), (pile_x, pile_y), dxfattribs={'layer': 'DIMENSIONS'})

            # Add offset text with overlap prevention
            mid_x = (load_x + pile_x) / 2
            mid_y = (load_y + pile_y) / 2
            offset_label = f"Offset: {offset:.2f}m"
            final_mid_x, final_mid_y = text_manager.add_text_position(mid_x, mid_y + 0.2, offset_label, 0.3)

            text = msp.add_text(offset_label, dxfattribs={'layer': 'TEXT', 'height': 0.3})
            text.set_placement((final_mid_x, final_mid_y), align=TextEntityAlignment.MIDDLE_CENTER)

    except Exception as e:
        if log_callback:
            log_callback(f"Error plotting pile centroid: {e}")


def plot_enhanced_pile_dxf(msp, x: float, y: float, pile_spec=None, pile_number: int = 1,
                         text_manager=None, log_callback=None) -> bool:
    """
    Plot a pile with enhanced visualization based on pile type specification.

    Args:
        msp: DXF model space
        x, y: Pile coordinates
        pile_spec: PileTypeSpec object or dictionary with pile details
        pile_number: Pile number for labeling
        text_manager: TextPlacementManager for label positioning
        log_callback: Logging callback

    Returns:
        bool: True if successfully plotted, False otherwise
    """
    try:
        from .pile_drawer import draw_pile_by_type

        # Pile specification is required
        if not pile_spec:
            raise ValueError("Pile specification is required for enhanced pile visualization")

        # Handle both dictionary and object formats
        if isinstance(pile_spec, dict):
            # Dictionary format from integration engine
            pile_type_str = pile_spec.get('type', 'UNKNOWN')
            diameter = pile_spec.get('diameter', None)
        else:
            # Object format (PileTypeSpec)
            try:
                pile_type = pile_spec.pile_type

                if pile_type == PileType.DHP:
                    pile_type_str = "DHP"
                elif pile_type == PileType.SHP:
                    pile_type_str = "SHP"
                elif pile_type == PileType.BP:
                    pile_type_str = "BP"
                else:
                    pile_type_str = str(pile_type.value)

                diameter = pile_spec.diameter if hasattr(pile_spec, 'diameter') else None
            except Exception as e:
                if log_callback:
                    log_callback(f"      Error parsing pile spec object: {e}")
                raise ValueError(f"Invalid pile specification: {e}")

        # Draw the pile based on type
        if pile_type_str in ["DHP", "SHP"]:
            return draw_pile_by_type(msp, x, y, pile_type_str, None, pile_number, text_manager, log_callback)
        elif pile_type_str == "BP":
            # Diameter is required for BP piles
            if not diameter:
                raise ValueError("Diameter is required for BP pile visualization")
            return draw_pile_by_type(msp, x, y, "BP", diameter, pile_number, text_manager, log_callback)
        else:
            # Unknown pile type
            raise ValueError(f"Unknown pile type: {pile_type_str}")

    except Exception as e:
        if log_callback:
            log_callback(f"      Error plotting enhanced pile: {e}")
        return False

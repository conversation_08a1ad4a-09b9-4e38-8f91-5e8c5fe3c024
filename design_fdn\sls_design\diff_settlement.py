"""
Differential Settlement Analysis Module for Foundation Design

This module provides comprehensive differential settlement analysis capabilities for pile 
foundation systems as part of Serviceability Limit State (SLS) design verification. 
It calculates and evaluates differential settlements between foundation elements to 
ensure structural serviceability requirements are met according to foundation design 
standards and codes of practice.

The module performs differential settlement analysis for:
- Vertical Elements (VE): Columns and structural walls
- Pile foundations: Individual pile elements and pile groups
- Distance-based settlement calculations using spatial coordinates
- SLS compliance checking against acceptance criteria (typically 1/500)

Key Features:
- Automated differential settlement matrix calculations
- Integration with SAFE structural analysis results
- Excel-based input/output processing for design workflows
- Comprehensive logging and progress tracking
- Failure identification and reporting for non-compliant elements

Author: Foundation Design Automation Team
Version: 1.0
"""

import numpy as np
import pandas as pd
from scipy.spatial import distance
from typing import Dict, List, Optional, Callable, Tuple, Any, Union
from design_fdn.design_fdn_config import (
    SHEET_VE_DSETT_FAIL, SHEET_PILE_DSETT_FAIL, SHEET_VE_DSETT_CHECK, 
    SHEET_VE_DSETT, SHEET_VE_DS, SHEET_VE_DISTANCE, SHEET_PILE_DSETT_CHECK,
    SHEET_PILE_DSETT, SHEET_PILE_DS, SHEET_PILE_DISTANCE
)


def check_differential_settlement(
    file_paths: Any,
    safe_mdbs: Any,
    excel_inputs: Any,
    excel_outputs: Any,
    design_results: Dict[str, Any],
    log_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Any]:
    """
    Perform comprehensive differential settlement analysis for pile foundation system.
    
    This function orchestrates the complete differential settlement analysis workflow,
    including processing of vertical elements (columns/walls) and pile foundations,
    calculating distance-based differential settlements, and generating design check
    reports with SLS compliance verification.
    
    The analysis implements industry-standard differential settlement calculation methods
    considering spatial relationships between foundation elements and applies code-based
    acceptance criteria (typically 1/500 angular distortion limit) for serviceability
    limit state design verification.
    """
    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback("📐 DIFFERENTIAL SETTLEMENT ANALYSIS - SLS VERIFICATION")
        log_callback("════════════════════════════════════════════════════")
        log_callback("   • Analyzing settlement differences between foundation elements")
        log_callback("   • Evaluating angular distortion compliance (SLS limit: 1/500)")
        log_callback("   • Processing vertical elements (columns & walls) and pile foundations")
        log_callback("   • Generating comprehensive serviceability assessment reports")
        log_callback("")
    
    # Create VE point mapping for columns and walls
    # This mapping associates design elements with their corresponding SAFE analysis points
    ve_mappings = _create_ve_mappings(excel_inputs)

    # Extract coordinate data for analysis points at foundation level (Z=0)
    # Filter for ground level points to focus on foundation elements
    df_point_coord = safe_mdbs.PointCoord[safe_mdbs.PointCoord['GlobalZ'] == 0].copy()
    
    # Identify pile points using naming convention (contains '_T' suffix)
    # This follows standard SAFE modeling conventions for pile elements
    df_point_coord_pile = df_point_coord[df_point_coord['Point'].astype(str).str.contains('_T')].copy()

    # Process VE (Vertical Element) points for columns and walls
    # Extract unique SAFE points associated with vertical structural elements
    ve_points = list(ve_mappings['SAFE_Point'].unique())
    df_point_coord_ve = df_point_coord[df_point_coord['Point'].isin(ve_points)].copy()
    df_point_coord_ve = df_point_coord_ve.drop_duplicates(subset=['Point'])

    # Get settlement displacement data from SAFE analysis results
    # This contains nodal displacements for all load cases and points
    df_nodal_displacements = excel_outputs.Settlement.copy()

    # Calculate differential settlement for VE points (columns and walls)
    # This analyzes settlement differences between vertical structural elements
    ve_results = _calculate_differential_settlement(
        df_point_coord_ve, df_nodal_displacements, ve_points, 'VE', log_callback
    )

    # Apply VE mappings to transform SAFE point names to design element names
    # This ensures results are reported using meaningful design nomenclature
    ve_mapping_dict = ve_mappings.set_index('SAFE_Point')['VE_Point'].to_dict()
    for df in ve_results.values():
        if hasattr(df, 'index'):
            # Handle multi-index dataframes (differential settlement matrices)
            if isinstance(df.index, pd.MultiIndex):
                df.index = df.index.set_levels(
                    df.index.levels[1].map(lambda x: ve_mapping_dict.get(x, x)), level=1
                )
            else:
                # Handle single-index dataframes
                df.index = df.index.map(lambda x: ve_mapping_dict.get(x, x))
        if hasattr(df, 'columns'):
            # Update column names to use design element names
            df.columns = df.columns.map(lambda x: ve_mapping_dict.get(x, x))

    # Calculate differential settlement for pile points
    # This analyzes settlement differences between individual pile elements
    pile_points = df_point_coord_pile['Point'].unique().tolist()
    pile_results = _calculate_differential_settlement(
        df_point_coord_pile, df_nodal_displacements, pile_points, 'Pile', log_callback
    )

    # Create SLS compliance check matrices
    # Apply 1/500 differential settlement limit as acceptance criterion
    ve_check = ve_results['diff_settlement'].copy()
    ve_check[ve_check >= 500] = 'OK'  # Mark compliant elements as 'OK'

    pile_check = pile_results['diff_settlement'].copy()
    pile_check[pile_check >= 500] = 'OK'  # Mark compliant elements as 'OK'

    # Create failure identification matrices
    # Isolate non-compliant elements for detailed reporting
    ve_fail = ve_results['diff_settlement'].copy()
    ve_fail[ve_fail >= 500] = None  # Remove compliant elements from failure report

    pile_fail = pile_results['diff_settlement'].copy()
    pile_fail[pile_fail >= 500] = None  # Remove compliant elements from failure report

    # Generate Excel output files with comprehensive analysis results
    # Save failure report for non-compliant elements
    with pd.ExcelWriter(file_paths.ResultDifferentialSettlementFail) as writer:
        ve_fail.to_excel(writer, sheet_name=SHEET_VE_DSETT_FAIL, index=True)
        pile_fail.to_excel(writer, sheet_name=SHEET_PILE_DSETT_FAIL, index=True)

    # Save complete analysis results with all calculation matrices
    with pd.ExcelWriter(file_paths.ResultDifferentialSettlement) as writer:
        # VE analysis results
        ve_check.to_excel(writer, sheet_name=SHEET_VE_DSETT_CHECK, index=True)
        ve_results['diff_settlement'].to_excel(writer, sheet_name=SHEET_VE_DSETT, index=True)
        ve_results['abs_ds'].to_excel(writer, sheet_name=SHEET_VE_DS, index=True)
        ve_results['dist'].to_excel(writer, sheet_name=SHEET_VE_DISTANCE, index=True)
        
        # Pile analysis results
        pile_check.to_excel(writer, sheet_name=SHEET_PILE_DSETT_CHECK, index=True)
        pile_results['diff_settlement'].to_excel(writer, sheet_name=SHEET_PILE_DSETT, index=True)
        pile_results['abs_ds'].to_excel(writer, sheet_name=SHEET_PILE_DS, index=True)
        pile_results['dist'].to_excel(writer, sheet_name=SHEET_PILE_DISTANCE, index=True)

    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback("✅ DIFFERENTIAL SETTLEMENT ANALYSIS - COMPLETED SUCCESSFULLY")
        log_callback("════════════════════════════════════════════════════")
        log_callback("   • VE (Vertical Elements) differential settlement evaluation completed")
        log_callback("   • Pile foundation differential settlement analysis completed")
        log_callback("   • SLS compliance verification matrices generated")
        log_callback("   • Comprehensive design check reports saved to output files")
        log_callback("")

    # Perform final SLS compliance assessment and reporting
    # Evaluate overall foundation performance against serviceability criteria
    for name, results in [('Column or Wall', ve_results), ('Piles', pile_results)]:
        # Find maximum (most critical) differential settlement
        max_ds = results['diff_settlement'].min().min()
        if log_callback:
            log_callback(f"📊 {name} Analysis Results:")
            log_callback(f"   • Maximum Differential Settlement: 1/{max_ds:.0f}")
            if max_ds < 500:
                log_callback(f"   ❌ FAILED: Differential Settlement > 1/500 (Exceeds SLS Limit)")
                log_callback(f"   • Action Required: Review foundation layout and pile spacing")
            else:
                log_callback(f"   ✅ PASSED: Differential Settlement ≤ 1/500 (Within SLS Limit)")
                log_callback(f"   • Foundation performance meets serviceability requirements")

    return design_results


def _create_ve_mappings(excel_inputs: Any) -> pd.DataFrame:
    """
    Create vertical element (VE) point mappings for differential settlement analysis.
    
    This function establishes the relationship between design elements (columns and walls)
    and their corresponding SAFE analysis points, enabling proper result mapping and
    reporting using meaningful design nomenclature rather than analysis point names.
    """
    ve_data = []

    # Process column element mappings
    # Each column is associated with its center point for settlement analysis
    for _, row in excel_inputs.Column.iterrows():
        ve_data.append({
            'VE_Point': row['Column'],        # Design column identifier
            'SAFE_Point': row['Center Point'] # Corresponding analysis point
        })

    # Process wall element mappings
    # Each wall is associated with its center point for settlement analysis
    for _, row in excel_inputs.Wall.iterrows():
        ve_data.append({
            'VE_Point': row['Wall'],          # Design wall identifier
            'SAFE_Point': row['Center Point'] # Corresponding analysis point
        })

    return pd.DataFrame(ve_data)


def _calculate_differential_settlement(
    df_coords: pd.DataFrame,
    df_displacements: pd.DataFrame,
    points: List[str],
    element_type: str,
    log_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, pd.DataFrame]:
    """
    Calculate differential settlement matrices for foundation elements.
    
    This function performs the core differential settlement calculations using spatial
    coordinates and displacement data. It implements distance-based differential
    settlement analysis following established geotechnical engineering methods for
    foundation serviceability assessment.
    
    The calculation process involves:
    1. Spatial distance matrix computation between all point pairs
    2. Settlement difference matrix calculation for each load case
    3. Normalized differential settlement computation (angular distortion)
    4. Lower triangular matrix extraction to avoid duplication
    5. Multi-load case result compilation
    """
    # Filter displacement data for specified points
    # Ensure only relevant points are included in the analysis
    df_disp_filtered = df_displacements[df_displacements['Point'].isin(points)].copy()
    
    # Create categorical ordering to maintain consistent point sequence
    # This ensures reproducible results and proper matrix alignment
    df_disp_filtered['Point'] = pd.Categorical(df_disp_filtered['Point'], categories=points, ordered=True)
    df_disp_filtered = df_disp_filtered.sort_values('Point')

    # Ensure coordinate data follows the same sequence as specified points
    # This is critical for proper matrix operations and result interpretation
    df_coords_ordered = df_coords.set_index('Point').reindex(points).reset_index()
    
    # Validate coordinate data completeness
    # Missing coordinates would compromise the distance calculations
    missing_coords = df_coords_ordered['Point'].isnull().sum()
    if missing_coords > 0:
        if log_callback:
            missing_points = df_coords_ordered[df_coords_ordered['Point'].isnull()].index.tolist()
            log_callback(f"⚠️  DATA VALIDATION WARNING: Missing Coordinate Information")
            log_callback(f"   • {missing_coords} points are missing coordinate data")
            log_callback(f"   • Missing coordinate point indices: {missing_points}")
            log_callback(f"   • Impact: These points will be excluded from differential settlement analysis")
    
    # Create coordinate array in the correct order for distance calculations
    # Extract (x, y) coordinates for each point in the specified sequence
    coordinates = [(row['GlobalX'], row['GlobalY']) for _, row in df_coords_ordered.iterrows()]

    # Calculate Euclidean distance matrix between all point pairs
    # This forms the basis for differential settlement normalization
    dist_matrix = distance.cdist(coordinates, coordinates, 'euclidean')
    df_dist = pd.DataFrame(dist_matrix, index=points, columns=points)

    # Extract unique load cases from displacement data
    # Each load case requires separate differential settlement analysis
    sls_cases = df_disp_filtered['OutputCase'].unique().tolist()

    # Initialize settlement matrix for all load cases
    # Organize settlement data by point and load case for efficient processing
    df_settlement = pd.DataFrame(index=points, columns=sls_cases)
    
    # Process settlement data for each load case
    for sls in sls_cases:
        # Extract settlements for current load case
        mask = df_disp_filtered['OutputCase'] == sls
        sls_data = df_disp_filtered[mask].set_index('Point')['Uz (mm)'] / 1000  # Convert to meters
        
        # Ensure settlements are ordered according to points sequence
        # This maintains consistency with coordinate and distance matrices
        ordered_settlements = sls_data.reindex(points)
        
        # Validate settlement data completeness
        missing_settlements = ordered_settlements.isnull().sum()
        if missing_settlements > 0 and log_callback:
            log_callback(f"⚠️  SETTLEMENT DATA WARNING: Incomplete Settlement Data")
            log_callback(f"   • Load Case: {sls}")
            log_callback(f"   • Missing Data Points: {missing_settlements}")
            log_callback(f"   • Impact: These points will have null values in settlement analysis")
        
        df_settlement[sls] = ordered_settlements

    # Initialize differential settlement calculation variables
    diff_settlement_list = []
    abs_ds_last = None  # Store absolute settlement differences for final load case

    # Progress tracking for long calculations
    total_cases = len(sls_cases)
    if log_callback:
        log_callback("")
        log_callback("════════════════════════════════════════════════════")
        log_callback(f"🔄 DIFFERENTIAL SETTLEMENT CALCULATION - {element_type.upper()}")
        log_callback("════════════════════════════════════════════════════")
        log_callback(f"   • Processing {len(points)} {element_type.lower()} elements")
        log_callback(f"   • Analyzing {total_cases} SLS load cases")
        log_callback(f"   • Computing spatial distance matrices and angular distortions")
        log_callback("")

    # Calculate differential settlement for each load case
    for i, sls in enumerate(sls_cases):
        # Extract settlement values in correct point order
        settlement_values = df_settlement[sls].values
        
        # Validate settlement data consistency
        if len(settlement_values) != len(points):
            if log_callback:
                log_callback(f"⚠️  DATA CONSISTENCY WARNING: Settlement Array Size Mismatch")
                log_callback(f"   • Load Case: {sls}")
                log_callback(f"   • Expected Points: {len(points)}")
                log_callback(f"   • Available Settlement Values: {len(settlement_values)}")
                log_callback(f"   • Impact: May affect differential settlement calculation accuracy")
        
        # Create settlement difference matrices using broadcasting
        # s1: settlement matrix with rows = point i, columns = point j
        # s2: transposed settlement matrix for pairwise differences
        s1 = pd.DataFrame(np.tile(settlement_values.reshape(-1, 1), (1, len(points))),
                         index=points, columns=points)
        s2 = s1.T

        # Calculate absolute differential settlement matrix
        # |Settlement_i - Settlement_j| for all point pairs
        abs_ds = np.abs(s1 - s2)
        abs_ds_df = pd.DataFrame(abs_ds, index=points, columns=points)
        abs_ds_last = abs_ds_df  # Store for output

        # Calculate normalized differential settlement (angular distortion)
        # Angular distortion = |ΔSettlement| / Distance
        # Result expressed as 1/angular_distortion for code comparison
        with np.errstate(divide='ignore', invalid='ignore'):
            temp_ds = 1 / (abs_ds_df / df_dist.replace(0, 1))  # Avoid division by zero

        # Apply upper triangular mask to avoid result duplication
        # Only lower triangular elements are meaningful (symmetric matrix)
        mask = np.triu(np.ones(temp_ds.shape, dtype=bool))
        temp_ds = temp_ds.mask(mask, None)

        # Add load case identifier for multi-case compilation
        temp_ds.insert(0, 'LOAD CASE', sls)
        diff_settlement_list.append(temp_ds)

        # Progress reporting for long calculations
        if log_callback and (i % 5 == 0 or i == total_cases - 1):
            progress = int((i + 1) / total_cases * 100)
            log_callback(f"🔄 Processing {element_type} Analysis: {progress}% completed ({i + 1}/{total_cases} load cases)")

    # Combine results from all load cases into multi-index dataframe
    # Create comprehensive differential settlement matrix with load case indexing
    df_diff_settlement = pd.concat(diff_settlement_list).reset_index()
    df_diff_settlement.set_index(['LOAD CASE', 'index'], inplace=True)
    
    # Final validation and logging
    if log_callback:
        log_callback("")
        log_callback(f"✅ {element_type} differential settlement calculation completed successfully")
        log_callback(f"   • Angular distortion matrices generated for all load cases")
        log_callback(f"   • Distance matrices computed for spatial relationships")
        log_callback(f"   • Settlement difference matrices compiled")
        if abs_ds_last is not None:
            # Verify the indices match the original points sequence
            if list(abs_ds_last.index) == points and list(abs_ds_last.columns) == points:
                log_callback(f"   • Data integrity verified: Point sequence maintained throughout calculations")
            else:
                log_callback(f"⚠️  DATA INTEGRITY WARNING: Point sequence may have been altered during calculations")
                log_callback(f"   • Impact: Results may not correspond to expected element order")
        log_callback("")

    return {
        'diff_settlement': df_diff_settlement,  # Normalized differential settlement (1/angular_distortion)
        'abs_ds': abs_ds_last,                  # Absolute settlement differences
        'dist': df_dist                         # Distance matrix between points
    }

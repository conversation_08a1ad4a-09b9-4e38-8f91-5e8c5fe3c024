"""
Foundation-Automation Build FEM Utility Functions - Structural Analysis Support Library

This module provides comprehensive utility functions for structural analysis calculations,
geometric computations, load transformations, and data processing operations required
for foundation and structural modeling workflows. It serves as a core computational
library supporting the Foundation-Automation system's analytical capabilities.

Key Features:
- Geometric calculations for structural elements (beams, walls, line loads)
- Coordinate transformation and angle computation for structural orientation
- Load transformation between global and local coordinate systems
- Core wall assembly and length calculation for lateral load systems
- Excel output processing for structured data export and reporting
- Load combination utilities for design code compliance
- Wind load pattern extraction and classification

Computational Categories:
- Element Geometry: Length and orientation calculations for linear structural elements
- Load Transformations: Coordinate system conversions for force and moment analysis
- Core Wall Processing: Assembly calculations for lateral load-resisting systems
- Data Export: Structured Excel output generation for analysis results
- Load Pattern Processing: Load combination and pattern classification utilities

Structural Analysis Applications:
- Foundation design with pile and mat foundation systems
- Lateral load analysis for core wall and shear wall systems
- Load combination generation for design code compliance
- Structural element sizing and orientation verification
- Analysis result processing and documentation

Mathematical Operations:
- Distance calculations using Euclidean geometry principles
- Angle computations with trigonometric functions and coordinate transformations
- Load transformation matrices for global-to-local coordinate conversions
- Moment distribution calculations for structural analysis
- Vector operations for force and moment resolution

Dependencies:
- datetime: Timestamp generation for logging and documentation
- math: Mathematical operations including distance, trigonometric functions
- numpy: Numerical array operations and mathematical computations
- pandas: DataFrame operations for structured data processing
- build_fem.build_fem_config: Configuration constants and sheet naming conventions

Technical Notes:
- All geometric calculations maintain engineering precision and accuracy
- Coordinate transformations handle both positive and negative angle orientations
- Load transformations account for structural element local axes and sign conventions
- Excel export functions preserve data integrity and formatting consistency
- Error handling ensures robust operation with invalid or missing data

Engineering Conventions:
- Coordinates follow right-hand rule convention for structural analysis
- Angles are measured counterclockwise from positive X-axis
- Load transformations maintain equilibrium and compatibility requirements
- Moment sign conventions follow structural engineering standards
- Unit consistency is maintained throughout all calculations (SI units preferred)

Author: Foundation-Automation Development Team
Version: Compatible with SAFE 16/22 structural analysis software
"""

from datetime import datetime
from math import dist
from math import radians, cos, sin
from typing import Optional, Callable, Tuple, Dict, Any, Union
import time
import traceback

import numpy as np
import pandas as pd

from build_fem import build_fem_config as config

# Enhanced logging system imports
try:
    from fdn_agent.pile_estimation.utils.logging_utils import (
        enhanced_log,
        log_function_entry,
        log_function_exit,
        log_validation_result,
        log_calculation_result,
        log_performance_metric,
        log_error_with_context,
        log_constraint_check,
        create_timed_logger
    )
    ENHANCED_LOGGING_AVAILABLE = True
except ImportError:
    ENHANCED_LOGGING_AVAILABLE = False

def cal_beam_length(excel_inputs, log_callback: Optional[Callable] = None) -> pd.DataFrame:
    """
    Calculate beam lengths and orientations from point coordinates for structural analysis.

    This function processes beam element definitions to compute geometric properties
    including length and orientation angle. These calculations are essential for
    structural analysis, load distribution, and design verification of beam elements.

    The calculation process includes:
    - Beam endpoint coordinate extraction from point definitions
    - Euclidean distance calculation between beam endpoints
    - Orientation angle computation using coordinate differences
    - Special handling for vertical beams (infinite slope condition)
    """
    # Enhanced logging and performance tracking
    log_function_entry(log_callback, "cal_beam_length",
                      beam_count=len(excel_inputs.Beam) if hasattr(excel_inputs, 'Beam') else 0)

    with create_timed_logger(log_callback, "beam_length_calculation") as timer:
        try:
            # Input validation
            if not hasattr(excel_inputs, 'Beam') or not hasattr(excel_inputs, 'Point'):
                raise ValueError("Excel inputs must contain both Beam and Point data")

            if excel_inputs.Beam.empty:
                enhanced_log(log_callback, "No beam data found, returning empty DataFrame", 'WARNING')
                return pd.DataFrame(columns=['Beam Mark', 'Beam Length (m)', 'Beam Theta (deg)'])

            if excel_inputs.Point.empty:
                raise ValueError("Point data is required but not found")

            log_validation_result(log_callback, "input_data_validation", True,
                                f"Found {len(excel_inputs.Beam)} beams and {len(excel_inputs.Point)} points")

            # Initialize result DataFrame
            df_beam_length = pd.DataFrame(
                columns=['Beam Mark', 'Beam Length (m)', 'Beam Theta (deg)'])

            # Create working copies to avoid modifying original data
            df_beam = excel_inputs.Beam.copy()
            df_point = excel_inputs.Point.copy()

            # Validate required columns
            required_beam_cols = ['Beam', 'Points']
            required_point_cols = ['Point', 'X (m)', 'Y (m)']

            missing_beam_cols = [col for col in required_beam_cols if col not in df_beam.columns]
            missing_point_cols = [col for col in required_point_cols if col not in df_point.columns]

            if missing_beam_cols:
                raise KeyError(f"Missing required beam columns: {missing_beam_cols}")
            if missing_point_cols:
                raise KeyError(f"Missing required point columns: {missing_point_cols}")

            log_validation_result(log_callback, "column_validation", True, "All required columns found")

            # Rename beam column for consistency
            df_beam.rename(columns={'Beam': 'Line (Text)'}, inplace=True)

            # Split points string into individual point references
            try:
                df_beam[['PointI (Text)', 'PointJ (Text)']] = df_beam['Points'].str.split(';', expand=True)
            except Exception as e:
                raise ValueError(f"Error parsing beam point references: {str(e)}")

            # Create point lookup dictionary for better performance
            point_coords = {}
            for _, row in df_point.iterrows():
                point_name = row['Point']
                point_coords[point_name] = (row['X (m)'], row['Y (m)'])

            enhanced_log(log_callback, f"Created point lookup for {len(point_coords)} points", 'DEBUG')

            # Process each beam
            processed_beams = 0
            failed_beams = 0

            for i in range(df_beam.index.size):
                try:
                    beam_name = df_beam.loc[i, 'Line (Text)']
                    point_i = df_beam.loc[i, 'PointI (Text)']
                    point_j = df_beam.loc[i, 'PointJ (Text)']

                    # Validate point references
                    if point_i not in point_coords:
                        enhanced_log(log_callback, f"Point {point_i} not found for beam {beam_name}", 'ERROR')
                        failed_beams += 1
                        continue

                    if point_j not in point_coords:
                        enhanced_log(log_callback, f"Point {point_j} not found for beam {beam_name}", 'ERROR')
                        failed_beams += 1
                        continue

                    # Get coordinates
                    point_i_x, point_i_y = point_coords[point_i]
                    point_j_x, point_j_y = point_coords[point_j]

                    # Calculate beam length
                    beam_length = dist([point_i_x, point_i_y], [point_j_x, point_j_y])

                    # Validate beam length
                    if beam_length < 1e-6:  # Very small length threshold
                        enhanced_log(log_callback, f"Warning: Beam {beam_name} has very small length ({beam_length:.6f}m)", 'WARNING')

                    # Calculate orientation angle
                    dx = point_j_x - point_i_x
                    dy = point_j_y - point_i_y

                    if abs(dx) < 1e-10:  # Essentially vertical beam
                        beam_theta = 90.0
                    else:
                        beam_theta = np.rad2deg(np.arctan(dy / dx))

                    # Add to results
                    row = df_beam_length.index.size
                    df_beam_length.loc[row] = [beam_name, beam_length, beam_theta]
                    processed_beams += 1

                    # Log progress for large datasets
                    if processed_beams % 100 == 0:
                        enhanced_log(log_callback, f"Processed {processed_beams} beams", 'DEBUG')

                except Exception as e:
                    failed_beams += 1
                    beam_name = df_beam.loc[i, 'Line (Text)'] if i < len(df_beam) else f"beam_{i}"
                    log_error_with_context(log_callback, e, f"processing beam {beam_name}")
                    continue

            # Log final results
            log_calculation_result(log_callback, "beam_processing",
                                 f"{processed_beams} successful, {failed_beams} failed", "beams")

            if failed_beams > 0:
                enhanced_log(log_callback, f"Warning: {failed_beams} beams failed processing", 'WARNING')

            log_performance_metric(log_callback, "beams_per_second",
                                 processed_beams / timer.get_duration() if timer.get_duration() and timer.get_duration() > 0 else 0,
                                 "beams/s")

            log_function_exit(log_callback, "cal_beam_length",
                            result=f"DataFrame with {len(df_beam_length)} beams",
                            processed_count=processed_beams, failed_count=failed_beams)

            return df_beam_length

        except Exception as e:
            log_error_with_context(log_callback, e, "cal_beam_length")
            raise


def cal_line_length(excel_inputs, log_callback: Optional[Callable] = None) -> pd.DataFrame:
    """
    Calculate line load lengths and orientations for distributed load analysis.

    This function processes line load element definitions to compute geometric
    properties essential for distributed load application and structural analysis.
    Line loads represent distributed forces along linear paths such as wall loads,
    equipment loads, or other continuous loading conditions.

    The calculation process includes:
    - Line load endpoint coordinate extraction from point definitions
    - Euclidean distance calculation for load distribution length
    - Orientation angle computation for proper load direction analysis
    - Coordinate transformation support for load application
    """
    # Enhanced logging and performance tracking
    log_function_entry(log_callback, "cal_line_length",
                      line_load_count=len(excel_inputs.LineLoad) if hasattr(excel_inputs, 'LineLoad') else 0)

    with create_timed_logger(log_callback, "line_length_calculation") as timer:
        try:
            # Input validation
            if not hasattr(excel_inputs, 'LineLoad') or not hasattr(excel_inputs, 'Point'):
                raise ValueError("Excel inputs must contain both LineLoad and Point data")

            if excel_inputs.LineLoad.empty:
                enhanced_log(log_callback, "No line load data found, returning empty DataFrame", 'WARNING')
                return pd.DataFrame(columns=['Line Load', 'Line Length (m)', 'Line Theta (deg)'])

            if excel_inputs.Point.empty:
                raise ValueError("Point data is required but not found")

            log_validation_result(log_callback, "input_data_validation", True,
                                f"Found {len(excel_inputs.LineLoad)} line loads and {len(excel_inputs.Point)} points")

            # Initialize result DataFrame
            df_line_length = pd.DataFrame(
                columns=['Line Load', 'Line Length (m)', 'Line Theta (deg)'])

            # Create working copies
            df_lineload = excel_inputs.LineLoad.copy()
            df_point = excel_inputs.Point.copy()

            # Validate required columns
            required_lineload_cols = ['Line Load', 'Points']
            required_point_cols = ['Point', 'X (m)', 'Y (m)']

            missing_lineload_cols = [col for col in required_lineload_cols if col not in df_lineload.columns]
            missing_point_cols = [col for col in required_point_cols if col not in df_point.columns]

            if missing_lineload_cols:
                raise KeyError(f"Missing required line load columns: {missing_lineload_cols}")
            if missing_point_cols:
                raise KeyError(f"Missing required point columns: {missing_point_cols}")

            log_validation_result(log_callback, "column_validation", True, "All required columns found")

            # Create point lookup dictionary for better performance
            point_coords = {}
            for _, row in df_point.iterrows():
                point_name = row['Point']
                point_coords[point_name] = (row['X (m)'], row['Y (m)'])

            enhanced_log(log_callback, f"Created point lookup for {len(point_coords)} points", 'DEBUG')

            # Split points string for all line loads at once (vectorized operation)
            try:
                df_lineload[['PointI (Text)', 'PointJ (Text)']] = df_lineload['Points'].str.split(';', expand=True)
            except Exception as e:
                raise ValueError(f"Error parsing line load point references: {str(e)}")

            # Process each line load
            processed_lines = 0
            failed_lines = 0

            for i in range(df_lineload.index.size):
                try:
                    lineload_name = df_lineload.loc[i, 'Line Load']
                    point_i = df_lineload.loc[i, 'PointI (Text)']
                    point_j = df_lineload.loc[i, 'PointJ (Text)']

                    # Validate point references
                    if point_i not in point_coords:
                        enhanced_log(log_callback, f"Point {point_i} not found for line load {lineload_name}", 'ERROR')
                        failed_lines += 1
                        continue

                    if point_j not in point_coords:
                        enhanced_log(log_callback, f"Point {point_j} not found for line load {lineload_name}", 'ERROR')
                        failed_lines += 1
                        continue

                    # Get coordinates
                    point_i_x, point_i_y = point_coords[point_i]
                    point_j_x, point_j_y = point_coords[point_j]

                    # Calculate line length
                    line_length = dist([point_i_x, point_i_y], [point_j_x, point_j_y])

                    # Validate line length
                    if line_length < 1e-6:  # Very small length threshold
                        enhanced_log(log_callback, f"Warning: Line load {lineload_name} has very small length ({line_length:.6f}m)", 'WARNING')

                    # Calculate orientation angle
                    dx = point_j_x - point_i_x
                    dy = point_j_y - point_i_y

                    if abs(dx) < 1e-10:  # Essentially vertical line
                        line_theta = 90.0
                    else:
                        line_theta = np.rad2deg(np.arctan(dy / dx))

                    # Add to results
                    row = df_line_length.index.size
                    df_line_length.loc[row] = [lineload_name, line_length, line_theta]
                    processed_lines += 1

                    # Log progress for large datasets
                    if processed_lines % 50 == 0:
                        enhanced_log(log_callback, f"Processed {processed_lines} line loads", 'DEBUG')

                except Exception as e:
                    failed_lines += 1
                    lineload_name = df_lineload.loc[i, 'Line Load'] if i < len(df_lineload) else f"line_load_{i}"
                    log_error_with_context(log_callback, e, f"processing line load {lineload_name}")
                    continue

            # Log final results
            log_calculation_result(log_callback, "line_load_processing",
                                 f"{processed_lines} successful, {failed_lines} failed", "line loads")

            if failed_lines > 0:
                enhanced_log(log_callback, f"Warning: {failed_lines} line loads failed processing", 'WARNING')

            log_performance_metric(log_callback, "line_loads_per_second",
                                 processed_lines / timer.get_duration() if timer.get_duration() and timer.get_duration() > 0 else 0,
                                 "loads/s")

            log_function_exit(log_callback, "cal_line_length",
                            result=f"DataFrame with {len(df_line_length)} line loads",
                            processed_count=processed_lines, failed_count=failed_lines)

            return df_line_length

        except Exception as e:
            log_error_with_context(log_callback, e, "cal_line_length")
            raise


def cal_wall_length(excel_inputs, log_callback: Optional[Callable] = None) -> pd.DataFrame:
    """
    Calculate wall element lengths and orientations for lateral load analysis.

    This function processes wall element definitions to compute geometric properties
    essential for lateral load resistance analysis, seismic design, and wind load
    distribution. Wall elements are critical components in lateral force-resisting
    systems and require accurate geometric characterization.

    The calculation process includes:
    - Wall segment endpoint coordinate extraction from point definitions
    - Length calculation for wall segment capacity and stiffness analysis
    - Orientation angle computation for load direction and resistance analysis
    - Geometric validation for structural modeling requirements
    """
    # Enhanced logging and performance tracking
    log_function_entry(log_callback, "cal_wall_length",
                      wall_count=len(excel_inputs.Wall) if hasattr(excel_inputs, 'Wall') else 0)

    with create_timed_logger(log_callback, "wall_length_calculation") as timer:
        try:
            # Input validation
            if not hasattr(excel_inputs, 'Wall') or not hasattr(excel_inputs, 'Point'):
                raise ValueError("Excel inputs must contain both Wall and Point data")

            if excel_inputs.Wall.empty:
                enhanced_log(log_callback, "No wall data found, returning empty DataFrame", 'WARNING')
                return pd.DataFrame(columns=['Wall Name', 'Wall Length (m)', 'Wall Theta (deg)'])

            if excel_inputs.Point.empty:
                raise ValueError("Point data is required but not found")

            log_validation_result(log_callback, "input_data_validation", True,
                                f"Found {len(excel_inputs.Wall)} walls and {len(excel_inputs.Point)} points")

            # Initialize result DataFrame
            df_wall_length = pd.DataFrame(
                columns=['Wall Name', 'Wall Length (m)', 'Wall Theta (deg)'])

            # Create working copies
            df_wall = excel_inputs.Wall.copy()
            df_point = excel_inputs.Point.copy()

            # Validate required columns
            required_wall_cols = ['Wall', 'Points']
            required_point_cols = ['Point', 'X (m)', 'Y (m)']

            missing_wall_cols = [col for col in required_wall_cols if col not in df_wall.columns]
            missing_point_cols = [col for col in required_point_cols if col not in df_point.columns]

            if missing_wall_cols:
                raise KeyError(f"Missing required wall columns: {missing_wall_cols}")
            if missing_point_cols:
                raise KeyError(f"Missing required point columns: {missing_point_cols}")

            log_validation_result(log_callback, "column_validation", True, "All required columns found")

            # Create point lookup dictionary for better performance
            point_coords = {}
            for _, row in df_point.iterrows():
                point_name = row['Point']
                point_coords[point_name] = (row['X (m)'], row['Y (m)'])

            enhanced_log(log_callback, f"Created point lookup for {len(point_coords)} points", 'DEBUG')

            # Split points string for all walls at once (vectorized operation)
            try:
                df_wall[['PointI (Text)', 'PointJ (Text)']] = df_wall['Points'].str.split(';', expand=True)
            except Exception as e:
                raise ValueError(f"Error parsing wall point references: {str(e)}")

            # Process each wall
            processed_walls = 0
            failed_walls = 0

            for i in range(df_wall.index.size):
                try:
                    wall_name = df_wall.loc[i, 'Wall']
                    point_i = df_wall.loc[i, 'PointI (Text)']
                    point_j = df_wall.loc[i, 'PointJ (Text)']

                    # Validate point references
                    if point_i not in point_coords:
                        enhanced_log(log_callback, f"Point {point_i} not found for wall {wall_name}", 'ERROR')
                        failed_walls += 1
                        continue

                    if point_j not in point_coords:
                        enhanced_log(log_callback, f"Point {point_j} not found for wall {wall_name}", 'ERROR')
                        failed_walls += 1
                        continue

                    # Get coordinates
                    point_i_x, point_i_y = point_coords[point_i]
                    point_j_x, point_j_y = point_coords[point_j]

                    # Calculate wall length
                    wall_length = dist([point_i_x, point_i_y], [point_j_x, point_j_y])

                    # Validate wall length
                    if wall_length < 1e-6:  # Very small length threshold
                        enhanced_log(log_callback, f"Warning: Wall {wall_name} has very small length ({wall_length:.6f}m)", 'WARNING')

                    # Calculate orientation angle
                    dx = point_j_x - point_i_x
                    dy = point_j_y - point_i_y

                    if abs(dx) < 1e-10:  # Essentially vertical wall
                        wall_theta = 90.0
                    else:
                        wall_theta = np.rad2deg(np.arctan(dy / dx))

                    # Add to results
                    row = df_wall_length.index.size
                    df_wall_length.loc[row] = [wall_name, wall_length, wall_theta]
                    processed_walls += 1

                    # Log progress for large datasets
                    if processed_walls % 50 == 0:
                        enhanced_log(log_callback, f"Processed {processed_walls} walls", 'DEBUG')

                except Exception as e:
                    failed_walls += 1
                    wall_name = df_wall.loc[i, 'Wall'] if i < len(df_wall) else f"wall_{i}"
                    log_error_with_context(log_callback, e, f"processing wall {wall_name}")
                    continue

            # Log final results
            log_calculation_result(log_callback, "wall_processing",
                                 f"{processed_walls} successful, {failed_walls} failed", "walls")

            if failed_walls > 0:
                enhanced_log(log_callback, f"Warning: {failed_walls} walls failed processing", 'WARNING')

            log_performance_metric(log_callback, "walls_per_second",
                                 processed_walls / timer.get_duration() if timer.get_duration() and timer.get_duration() > 0 else 0,
                                 "walls/s")

            log_function_exit(log_callback, "cal_wall_length",
                            result=f"DataFrame with {len(df_wall_length)} walls",
                            processed_count=processed_walls, failed_count=failed_walls)

            return df_wall_length

        except Exception as e:
            log_error_with_context(log_callback, e, "cal_wall_length")
            raise


def cal_corewall_length(excel_inputs, df_wall_length: pd.DataFrame, log_callback: Optional[Callable] = None) -> pd.DataFrame:
    """
    Calculate total core wall lengths by assembling individual wall segments into systems.

    This function processes wall group assignments to compute total core wall lengths
    for lateral load-resisting systems. Core walls are assemblies of individual wall
    segments that work together to resist lateral forces from wind and seismic loads.
    The total length affects both strength and stiffness characteristics.

    The calculation process includes:
    - Wall group identification from structural input definitions
    - Individual wall segment length extraction from pre-calculated data
    - Length summation for each core wall assembly system
    - Validation of wall group assignments and geometric consistency
    """
    # Enhanced logging and performance tracking
    log_function_entry(log_callback, "cal_corewall_length",
                      wall_count=len(excel_inputs.Wall) if hasattr(excel_inputs, 'Wall') else 0,
                      wall_length_count=len(df_wall_length))

    with create_timed_logger(log_callback, "corewall_length_calculation") as timer:
        try:
            # Input validation
            if not hasattr(excel_inputs, 'Wall'):
                raise ValueError("Excel inputs must contain Wall data")

            if excel_inputs.Wall.empty:
                enhanced_log(log_callback, "No wall data found, returning empty DataFrame", 'WARNING')
                return pd.DataFrame(columns=['CoreWall Name', 'CoreWall Length (m)'])

            if df_wall_length.empty:
                enhanced_log(log_callback, "No wall length data provided, returning empty DataFrame", 'WARNING')
                return pd.DataFrame(columns=['CoreWall Name', 'CoreWall Length (m)'])

            # Validate required columns
            if 'Wall Group' not in excel_inputs.Wall.columns:
                raise KeyError("Missing required 'Wall Group' column in wall data")

            required_wall_length_cols = ['Wall Name', 'Wall Length (m)']
            missing_cols = [col for col in required_wall_length_cols if col not in df_wall_length.columns]
            if missing_cols:
                raise KeyError(f"Missing required wall length columns: {missing_cols}")

            log_validation_result(log_callback, "input_validation", True, "All required data and columns found")

            # Initialize result DataFrame
            df_core_wall_length = pd.DataFrame(
                columns=['CoreWall Name', 'CoreWall Length (m)'])

            # Create working copy
            df_wall = excel_inputs.Wall.copy()

            # Get unique wall groups
            unique_wall_groups = df_wall['Wall Group'].unique()

            # Remove any NaN values from wall groups
            unique_wall_groups = [group for group in unique_wall_groups if pd.notna(group)]

            if not unique_wall_groups:
                enhanced_log(log_callback, "No valid wall groups found", 'WARNING')
                return df_core_wall_length

            log_calculation_result(log_callback, "wall_groups_found", len(unique_wall_groups), "groups")

            # Create wall length lookup dictionary for better performance
            wall_length_lookup = {}
            for _, row in df_wall_length.iterrows():
                wall_name = row['Wall Name']
                wall_length = row['Wall Length (m)']
                wall_length_lookup[wall_name] = wall_length

            enhanced_log(log_callback, f"Created wall length lookup for {len(wall_length_lookup)} walls", 'DEBUG')

            # Process each core wall group
            processed_groups = 0
            total_walls_processed = 0
            missing_walls = 0

            for core_wall_name in unique_wall_groups:
                try:
                    core_wall_length = 0.0
                    walls_in_group_count = 0

                    # Find all walls that belong to this core wall group
                    condition = df_wall['Wall Group'] == core_wall_name
                    walls_in_group = df_wall.loc[condition, 'Wall']

                    for wall_name in walls_in_group:
                        walls_in_group_count += 1

                        # Find the length of each wall in the group using lookup
                        if wall_name in wall_length_lookup:
                            wall_length = wall_length_lookup[wall_name]
                            core_wall_length += wall_length
                            total_walls_processed += 1
                        else:
                            enhanced_log(log_callback, f"Wall '{wall_name}' in group '{core_wall_name}' not found in wall length data", 'WARNING')
                            missing_walls += 1

                    # Add core wall to results
                    row = df_core_wall_length.index.size
                    df_core_wall_length.loc[row] = [core_wall_name, core_wall_length]
                    processed_groups += 1

                    log_calculation_result(log_callback, f"core_wall_{core_wall_name}_length",
                                         core_wall_length, "m")
                    enhanced_log(log_callback, f"Core wall '{core_wall_name}': {walls_in_group_count} walls, total length {core_wall_length:.3f}m", 'DEBUG')

                except Exception as e:
                    log_error_with_context(log_callback, e, f"processing core wall group {core_wall_name}")
                    continue

            # Log final results
            log_calculation_result(log_callback, "core_wall_processing",
                                 f"{processed_groups} groups, {total_walls_processed} walls processed", "")

            if missing_walls > 0:
                enhanced_log(log_callback, f"Warning: {missing_walls} walls were missing from wall length data", 'WARNING')

            log_performance_metric(log_callback, "core_walls_per_second",
                                 processed_groups / timer.get_duration() if timer.get_duration() and timer.get_duration() > 0 else 0,
                                 "groups/s")

            log_function_exit(log_callback, "cal_corewall_length",
                            result=f"DataFrame with {len(df_core_wall_length)} core walls",
                            processed_groups=processed_groups,
                            total_walls=total_walls_processed,
                            missing_walls=missing_walls)

            return df_core_wall_length

        except Exception as e:
            log_error_with_context(log_callback, e, "cal_corewall_length")
            raise


def transform_wall_load_global(t: float, length: float, deg: float, v_x: float, v_y: float,
                              f_z: float, m_x: float, m_y: float, m_z: float,
                              log_callback: Optional[Callable] = None) -> Tuple[float, ...]:
    """
    Transform wall loads from global coordinates to local element coordinates with moment corrections.
    
    This function performs comprehensive coordinate transformation for wall loads,
    converting global force and moment components to local element coordinates while
    accounting for geometric effects and pile cap interactions. The transformation
    is essential for accurate structural analysis and load distribution calculations.

    The transformation process includes:
    - Coordinate system rotation based on wall orientation angle
    - Moment transformation with sign convention handling for positive/negative slopes
    - Additional moment calculation due to pile cap shear effects
    - Distributed load calculation from local moment components
    - Complete force and moment component resolution
    """
    # Enhanced logging and performance tracking
    log_function_entry(log_callback, "transform_wall_load_global",
                      thickness=t, length=length, angle=deg)

    try:
        # Input validation
        if length <= 0:
            raise ValueError(f"Wall length must be positive, got {length}")

        if abs(length) < 1e-10:
            raise ZeroDivisionError("Wall length is too small for load transformation")

        log_validation_result(log_callback, "input_parameters", True,
                            f"t={t}, length={length}, deg={deg}")

        # Convert angle to radians
        rad = radians(deg)

        # Additional moment due to pile cap shear on top
        mx_add = -(v_y * t)
        my_add = v_x * t

        log_calculation_result(log_callback, "pile_cap_moments",
                             f"mx_add={mx_add:.3f}, my_add={my_add:.3f}", "kN-m")

        # Transform moments to local coordinate system
        if deg >= 0:
            # FOR POSITIVE SLOPE WALL, 0 deg WALL & 90 deg WALL
            m_1 = (cos(rad) * m_x + sin(rad) * m_y)
            m_3 = (sin(rad) * m_x - cos(rad) * m_y)
        else:
            # FOR NEGATIVE SLOPE WALL
            rad = radians(abs(deg))
            m_1 = (cos(rad) * m_x - sin(rad) * m_y)
            m_3 = (-sin(rad) * m_x - cos(rad) * m_y)

        log_calculation_result(log_callback, "local_moments",
                             f"m_1={m_1:.3f}, m_3={m_3:.3f}", "kN-m")

        # Line load due to moment (distributed load from moment)
        fz_m3 = (6 * m_3 / (length ** 2))

        # Transform additional moments
        if deg >= 0:
            # FOR POSITIVE SLOPE WALL, 0 deg WALL & 90 deg WALL
            m1_add = (cos(rad) * mx_add + sin(rad) * my_add)
            m3_add = (sin(rad) * mx_add - cos(rad) * my_add)
        else:
            # FOR NEGATIVE SLOPE WALL
            rad = radians(abs(deg))
            m1_add = (cos(rad) * mx_add - sin(rad) * my_add)
            m3_add = (-sin(rad) * mx_add - cos(rad) * my_add)

        # Additional line load due to moment of top pile cap shear
        fz_m3_add = (6 * m3_add / (length ** 2))

        # Calculate distributed loads
        s_x = v_x / length
        s_y = v_y / length
        s_z_start = f_z / length + fz_m3
        s_z_end = f_z / length - fz_m3
        s_m1 = m_1 / length

        s_z_start_add = fz_m3_add
        s_z_end_add = -fz_m3_add
        s_m1_add = m1_add / length

        s_mz = m_z / length

        log_calculation_result(log_callback, "distributed_loads",
                             f"s_x={s_x:.3f}, s_y={s_y:.3f}, s_z_start={s_z_start:.3f}", "kN/m")

        log_function_exit(log_callback, "transform_wall_load_global",
                        result="17 load components calculated")

        return (mx_add, my_add, m_1, m_3, fz_m3, m1_add, m3_add, fz_m3_add,
                s_x, s_y, s_z_start, s_z_end, s_m1, s_z_start_add, s_z_end_add, s_m1_add, s_mz)

    except Exception as e:
        log_error_with_context(log_callback, e, "transform_wall_load_global")
        raise


def transform_line_load_local(
    t: float,
    length: float,
    v_1: float,
    v_3: float,
    f_z: float,
    m_1: float,
    m_3: float,
    m_z: float
) -> Tuple[float, float, float, float, float, float, float, float, float, float, float, float, float, float, float]:
    """
    Transform line loads in local coordinates with pile cap interaction effects.
    
    This function processes line loads already in local element coordinates,
    applying pile cap interaction effects and converting moments to equivalent
    distributed loads. This transformation is essential for accurate structural
    analysis when loads are specified in element local coordinate systems.

    The transformation process includes:
    - Local moment scaling based on element length for proper intensity
    - Distributed load calculation from local moment components using beam theory
    - Pile cap interaction moment calculation due to shear force eccentricity
    - Additional distributed load determination from pile cap effects
    - Complete distributed load and moment component resolution
    """
    # per meter in kN/m and kNm/m

    m_3 = m_3 * length  # kNm

    # line load due to moment
    fz_m3 = (6 * m_3 / length ** 2)  # kN/m

    s_1 = v_1  # kN/m
    s_3 = v_3  # kN/m
    s_z_start = f_z - fz_m3  # kN/m
    s_z_end = f_z + fz_m3  # kN/m
    s_m1 = m_1  # kN/m
    s_mz = m_z  # kN/m

    # Additional moment due to pile cap shear on top
    m1_add = 0 + (v_3 * t)  # kNm/m
    m3_add = 0 - (v_1 * length * t)  # kNm

    # Additional line load due to moment of top pile cap shear
    fz_m3_add = (6 * m3_add / length ** 2)  # kN/m

    s_z_start_add = -fz_m3_add  # kN/m
    s_z_end_add = fz_m3_add  # kN/m
    s_m1_add = m1_add  # kN/m

    return m_1, m_3, fz_m3, m1_add, m3_add, fz_m3_add, s_1, s_3, s_z_start, s_z_end, s_m1, s_z_start_add, s_z_end_add, s_m1_add, s_mz


def export_output_loading(file_paths, excel_outputs, log_callback: Optional[Callable] = None) -> None:
    """
    Export comprehensive loading analysis results to structured Excel worksheets.
    
    This function processes and exports loading analysis results from various
    structural elements to organized Excel worksheets for documentation, review,
    and design verification. The export maintains data integrity while providing
    clear formatting for engineering review and reporting.

    The export process includes:
    - Multi-level column header processing for clear data organization
    - Separate worksheet creation for each structural element type
    - Header and data separation for improved readability
    - Timestamp logging for export tracking and documentation

    Exported Load Categories:
    - Point Loads: Concentrated forces and moments at specific locations
    - Line Loads: Distributed loads along linear elements and paths
    - Beam Loads: Beam element loading with distributed and concentrated components
    - Column Loads: Axial and lateral loads for vertical structural elements
    - Wall Loads: Lateral and vertical loads for wall elements
    - Core Wall Loads: Combined loading for core wall lateral systems
    """
    # Enhanced logging and performance tracking
    log_function_entry(log_callback, "export_output_loading",
                      output_file=str(file_paths.ExcelOutputLoading))

    with create_timed_logger(log_callback, "excel_export") as timer:
        try:
            # Input validation
            if not hasattr(file_paths, 'ExcelOutputLoading'):
                raise ValueError("File paths must contain ExcelOutputLoading attribute")

            if not hasattr(excel_outputs, 'PointLoad'):
                raise ValueError("Excel outputs must contain required load data")

            log_validation_result(log_callback, "input_validation", True, "File paths and excel outputs validated")

            # Extract load data with error handling
            try:
                df_output_load_point = excel_outputs.PointLoad
                df_output_load_line = excel_outputs.LineLoad
                df_output_load_beam = excel_outputs.BeamLoad
                df_output_load_column = excel_outputs.ColumnLoad
                df_output_load_wall = excel_outputs.WallLoad
                df_output_load_corewall = excel_outputs.CoreWallLoad
            except AttributeError as e:
                log_error_with_context(log_callback, e, "extracting load data from excel_outputs")
                raise ValueError(f"Missing required load data: {str(e)}")

            log_calculation_result(log_callback, "data_extraction", "6 load types extracted", "datasets")

            # Export to Excel with comprehensive error handling
            try:
                with pd.ExcelWriter(file_paths.ExcelOutputLoading) as writer:
                    worksheets_created = 0

                    # Point load record
                    try:
                        df_title = pd.DataFrame(columns=df_output_load_point.droplevel([1], axis=1).columns)
                        df_value = df_output_load_point.droplevel(0, axis=1)
                        df_title.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_POINT_LOAD, index=False)
                        df_value.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_POINT_LOAD, startrow=1, index=False)
                        worksheets_created += 1
                        enhanced_log(log_callback, f"Created point load worksheet with {len(df_value)} records", 'DEBUG')
                    except Exception as e:
                        log_error_with_context(log_callback, e, "creating point load worksheet")

                    # Line load record
                    try:
                        df_title = pd.DataFrame(columns=df_output_load_line.droplevel([1], axis=1).columns)
                        df_value = df_output_load_line.droplevel(0, axis=1)
                        df_title.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_LINE_LOAD, index=False)
                        df_value.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_LINE_LOAD, startrow=1, index=False)
                        worksheets_created += 1
                        enhanced_log(log_callback, f"Created line load worksheet with {len(df_value)} records", 'DEBUG')
                    except Exception as e:
                        log_error_with_context(log_callback, e, "creating line load worksheet")

                    # Beam load record
                    try:
                        df_title = pd.DataFrame(columns=df_output_load_beam.droplevel([1], axis=1).columns)
                        df_value = df_output_load_beam.droplevel(0, axis=1)
                        df_title.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_BEAM, index=False)
                        df_value.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_BEAM, startrow=1, index=False)
                        worksheets_created += 1
                        enhanced_log(log_callback, f"Created beam load worksheet with {len(df_value)} records", 'DEBUG')
                    except Exception as e:
                        log_error_with_context(log_callback, e, "creating beam load worksheet")

                    # Column load record
                    try:
                        df_title = pd.DataFrame(columns=df_output_load_column.droplevel([1], axis=1).columns)
                        df_value = df_output_load_column.droplevel(0, axis=1)
                        df_title.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_COLUMN, index=False)
                        df_value.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_COLUMN, startrow=1, index=False)
                        worksheets_created += 1
                        enhanced_log(log_callback, f"Created column load worksheet with {len(df_value)} records", 'DEBUG')
                    except Exception as e:
                        log_error_with_context(log_callback, e, "creating column load worksheet")

                    # Wall load record
                    try:
                        df_title = pd.DataFrame(columns=df_output_load_wall.droplevel([1], axis=1).columns)
                        df_value = df_output_load_wall.droplevel(0, axis=1)
                        df_title.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_WALL, index=False)
                        df_value.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_WALL, startrow=1, index=False)
                        worksheets_created += 1
                        enhanced_log(log_callback, f"Created wall load worksheet with {len(df_value)} records", 'DEBUG')
                    except Exception as e:
                        log_error_with_context(log_callback, e, "creating wall load worksheet")

                    # Core wall load record
                    try:
                        df_title = pd.DataFrame(columns=df_output_load_corewall.droplevel([1], axis=1).columns)
                        df_value = df_output_load_corewall.droplevel(0, axis=1)
                        df_title.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_CORE_WALL, index=False)
                        df_value.to_excel(writer, sheet_name=config.SHEET_OUTPUT_LOAD_CORE_WALL, startrow=1, index=False)
                        worksheets_created += 1
                        enhanced_log(log_callback, f"Created core wall load worksheet with {len(df_value)} records", 'DEBUG')
                    except Exception as e:
                        log_error_with_context(log_callback, e, "creating core wall load worksheet")

                    log_calculation_result(log_callback, "worksheets_created", worksheets_created, "sheets")

            except Exception as e:
                log_error_with_context(log_callback, e, "Excel file creation")
                raise

            # Log successful completion
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            success_message = f'{now} Export Excel Column Load Record'
            print(success_message)
            enhanced_log(log_callback, success_message, 'INFO')

            log_performance_metric(log_callback, "export_rate",
                                 worksheets_created / timer.get_duration() if timer.get_duration() and timer.get_duration() > 0 else 0,
                                 "sheets/s")

            log_function_exit(log_callback, "export_output_loading",
                            result=f"Excel file created with {worksheets_created} worksheets")

        except Exception as e:
            log_error_with_context(log_callback, e, "export_output_loading")
            raise


def _get_limit_state_settings(limit_state: str, log_callback: Optional[Callable] = None) -> Dict[str, str]:
    """
    Get the design settings for a load combination based on limit state.

    This function provides standardized design settings for structural analysis
    based on the specified limit state. The settings control various analysis
    parameters and design checks according to structural engineering standards.
    """
    log_function_entry(log_callback, "_get_limit_state_settings", limit_state=limit_state)

    try:
        # Input validation
        if limit_state not in ['ULS', 'SLS']:
            raise ValueError(f"Invalid limit state '{limit_state}'. Must be 'ULS' or 'SLS'")

        log_validation_result(log_callback, "limit_state_validation", True, f"Valid limit state: {limit_state}")

        if limit_state == 'ULS':
            settings = {
                'strength': 'Yes',
                'initialization': 'No',
                'norm': 'No',
                'long': 'No'
            }
            enhanced_log(log_callback, "Applied Ultimate Limit State (ULS) settings", 'DEBUG')
        else:  # SLS
            settings = {
                'strength': 'No',
                'initialization': 'Yes',
                'norm': 'Yes',
                'long': 'Yes'
            }
            enhanced_log(log_callback, "Applied Serviceability Limit State (SLS) settings", 'DEBUG')

        log_calculation_result(log_callback, "design_settings", f"{len(settings)} parameters configured", "settings")
        log_function_exit(log_callback, "_get_limit_state_settings", result=f"Settings for {limit_state}")

        return settings

    except Exception as e:
        log_error_with_context(log_callback, e, "_get_limit_state_settings")
        raise


def _get_wind_load_patterns(load_pattern_df: pd.DataFrame, log_callback: Optional[Callable] = None) -> np.ndarray:
    """
    Extract wind load pattern names from load pattern data.

    This function filters load pattern data to identify and extract wind load
    patterns for structural analysis. Wind load patterns are essential for
    lateral load analysis and building response calculations.
    """
    log_function_entry(log_callback, "_get_wind_load_patterns",
                      dataframe_shape=load_pattern_df.shape if load_pattern_df is not None else "None")

    try:
        # Input validation
        if load_pattern_df is None or load_pattern_df.empty:
            enhanced_log(log_callback, "Empty or None load pattern DataFrame provided", 'WARNING')
            return np.array([])

        # Validate required columns
        required_columns = ['Load Type', 'LoadPat (Text)']
        missing_columns = [col for col in required_columns if col not in load_pattern_df.columns]

        if missing_columns:
            raise KeyError(f"Missing required columns: {missing_columns}")

        log_validation_result(log_callback, "column_validation", True, "Required columns found")

        # Filter for wind load patterns
        condition = load_pattern_df['Load Type'] == 'WIND'
        wind_patterns = load_pattern_df.loc[condition, 'LoadPat (Text)'].values

        log_calculation_result(log_callback, "wind_patterns_found", len(wind_patterns), "patterns")

        if len(wind_patterns) > 0:
            enhanced_log(log_callback, f"Wind load patterns: {list(wind_patterns)}", 'DEBUG')
        else:
            enhanced_log(log_callback, "No wind load patterns found in data", 'WARNING')

        log_function_exit(log_callback, "_get_wind_load_patterns",
                        result=f"Array with {len(wind_patterns)} wind patterns")

        return wind_patterns

    except Exception as e:
        log_error_with_context(log_callback, e, "_get_wind_load_patterns")
        raise

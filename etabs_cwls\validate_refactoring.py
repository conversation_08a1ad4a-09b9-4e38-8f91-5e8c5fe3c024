"""
Comprehensive Validation Script for ETABS CWLS Refactoring

This script performs manual validation of the refactored modules to ensure
all integration points work correctly.
"""

import os
import sys
import ast
import importlib.util
from pathlib import Path
from typing import Dict, List, Set, Any

class RefactoringValidator:
    """Validates the refactored ETABS CWLS modules."""
    
    def __init__(self):
        self.etabs_cwls_path = Path(__file__).parent
        self.modules = [
            'console_interface',
            'configuration_manager',
            'file_path_manager', 
            'initialization_processor',
            'pier_force_processor',
            'joint_reaction_processor',
            'coordinate_transformer',
            'schedule_generator'
        ]
        self.validation_results = {}
    
    def validate_all(self) -> Dict[str, Any]:
        """Run all validation tests."""
        print("=" * 80)
        print("ETABS CWLS Refactoring Validation")
        print("=" * 80)
        
        self.validation_results['file_structure'] = self.validate_file_structure()
        self.validation_results['import_syntax'] = self.validate_import_syntax()
        self.validation_results['function_exports'] = self.validate_function_exports()
        self.validation_results['circular_dependencies'] = self.validate_circular_dependencies()
        self.validation_results['backward_compatibility'] = self.validate_backward_compatibility()
        self.validation_results['api_consistency'] = self.validate_api_consistency()
        
        self.print_summary()
        return self.validation_results
    
    def validate_file_structure(self) -> Dict[str, Any]:
        """Validate that all expected files exist."""
        print("\n1. Validating File Structure...")
        results = {'passed': 0, 'failed': 0, 'errors': []}
        
        # Check that all module files exist
        for module in self.modules:
            file_path = self.etabs_cwls_path / f"{module}.py"
            if file_path.exists():
                print(f"   ✓ {module}.py exists")
                results['passed'] += 1
            else:
                print(f"   ❌ {module}.py missing")
                results['failed'] += 1
                results['errors'].append(f"Missing file: {module}.py")
        
        # Check that _main.py exists and is refactored
        main_path = self.etabs_cwls_path / "_main.py"
        if main_path.exists():
            with open(main_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if len(content.split('\n')) < 200:  # Should be much smaller now
                print("   ✓ _main.py is refactored (small size)")
                results['passed'] += 1
            else:
                print("   ❌ _main.py appears to still be monolithic")
                results['failed'] += 1
                results['errors'].append("_main.py not properly refactored")
        else:
            print("   ❌ _main.py missing")
            results['failed'] += 1
            results['errors'].append("Missing _main.py")
        
        return results
    
    def validate_import_syntax(self) -> Dict[str, Any]:
        """Validate import syntax in all modules."""
        print("\n2. Validating Import Syntax...")
        results = {'passed': 0, 'failed': 0, 'errors': []}
        
        for module in self.modules + ['_main']:
            file_path = self.etabs_cwls_path / f"{module}.py"
            if not file_path.exists():
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Parse the file to check for syntax errors
                ast.parse(content)
                print(f"   ✓ {module}.py syntax valid")
                results['passed'] += 1
                
                # Check for problematic imports
                if 'tuple[' in content and 'from typing import' in content:
                    if 'Tuple' not in content:
                        print(f"   ⚠️  {module}.py uses tuple[] without Tuple import")
                        results['errors'].append(f"{module}.py: tuple[] syntax without Tuple import")
                
            except SyntaxError as e:
                print(f"   ❌ {module}.py syntax error: {e}")
                results['failed'] += 1
                results['errors'].append(f"{module}.py: {str(e)}")
            except Exception as e:
                print(f"   ❌ {module}.py validation error: {e}")
                results['failed'] += 1
                results['errors'].append(f"{module}.py: {str(e)}")
        
        return results
    
    def validate_function_exports(self) -> Dict[str, Any]:
        """Validate that modules export expected functions."""
        print("\n3. Validating Function Exports...")
        results = {'passed': 0, 'failed': 0, 'errors': []}
        
        expected_exports = {
            'console_interface': ['main', 'get_etabs_version_input', 'display_menu_and_get_selection'],
            'configuration_manager': ['assign_ETABS_converter', 'validate_system_configuration'],
            'file_path_manager': ['filepath_selection', 'configure_output_paths'],
            'initialization_processor': ['initialization', 'read_load_cases'],
            'pier_force_processor': ['create_schedule_pier_force', 'process_pier_forces'],
            'joint_reaction_processor': ['create_schedule_joint_reaction'],
            'coordinate_transformer': ['create_raw_schedule', 'integer_round'],
            'schedule_generator': ['create_final_schedule', 'merge_schedules']
        }
        
        for module, expected_funcs in expected_exports.items():
            file_path = self.etabs_cwls_path / f"{module}.py"
            if not file_path.exists():
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for __all__ export
                if '__all__' in content:
                    print(f"   ✓ {module}.py has __all__ export")
                    results['passed'] += 1
                else:
                    print(f"   ⚠️  {module}.py missing __all__ export")
                
                # Check for expected functions
                for func in expected_funcs:
                    if f"def {func}(" in content:
                        print(f"   ✓ {module}.py exports {func}")
                        results['passed'] += 1
                    else:
                        print(f"   ❌ {module}.py missing {func}")
                        results['failed'] += 1
                        results['errors'].append(f"{module}.py missing function: {func}")
                        
            except Exception as e:
                print(f"   ❌ Error validating {module}.py: {e}")
                results['failed'] += 1
                results['errors'].append(f"{module}.py validation error: {str(e)}")
        
        return results
    
    def validate_circular_dependencies(self) -> Dict[str, Any]:
        """Check for circular dependencies between modules."""
        print("\n4. Validating Circular Dependencies...")
        results = {'passed': 0, 'failed': 0, 'errors': [], 'dependencies': {}}
        
        # Extract imports from each module
        for module in self.modules:
            file_path = self.etabs_cwls_path / f"{module}.py"
            if not file_path.exists():
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                imports = []
                for line in content.split('\n'):
                    line = line.strip()
                    if line.startswith('from .') and 'import' in line:
                        import_module = line.split('from .')[1].split(' import')[0].strip()
                        if import_module in self.modules:
                            imports.append(import_module)
                
                results['dependencies'][module] = imports
                print(f"   ✓ {module} dependencies: {imports if imports else 'None'}")
                results['passed'] += 1
                
            except Exception as e:
                print(f"   ❌ Error analyzing {module}: {e}")
                results['failed'] += 1
                results['errors'].append(f"{module} analysis error: {str(e)}")
        
        # Check for circular dependencies
        circular_deps = self._detect_circular_deps(results['dependencies'])
        if circular_deps:
            print(f"   ❌ Circular dependencies found: {circular_deps}")
            results['failed'] += len(circular_deps)
            results['errors'].extend([f"Circular dependency: {dep}" for dep in circular_deps])
        else:
            print("   ✓ No circular dependencies detected")
            results['passed'] += 1
        
        return results
    
    def _detect_circular_deps(self, deps: Dict[str, List[str]]) -> List[str]:
        """Detect circular dependencies."""
        circular = []
        
        def has_cycle(module: str, target: str, visited: Set[str], path: List[str]) -> bool:
            if module == target and len(path) > 1:
                circular.append(" -> ".join(path + [module]))
                return True
            
            if module in visited:
                return False
            
            visited.add(module)
            
            for dep in deps.get(module, []):
                if has_cycle(dep, target, visited.copy(), path + [module]):
                    return True
            
            return False
        
        for module in deps:
            for dep in deps.get(module, []):
                has_cycle(dep, module, set(), [module])
        
        return circular
    
    def validate_backward_compatibility(self) -> Dict[str, Any]:
        """Validate backward compatibility."""
        print("\n5. Validating Backward Compatibility...")
        results = {'passed': 0, 'failed': 0, 'errors': []}
        
        # Check _main.py exports
        main_path = self.etabs_cwls_path / "_main.py"
        if main_path.exists():
            try:
                with open(main_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                expected_functions = [
                    'main', 'assign_ETABS_converter', 'filepath_selection',
                    'initialization', 'create_schedule_pier_force',
                    'create_schedule_joint_reaction', 'create_raw_schedule',
                    'create_final_schedule', 'integer_round'
                ]
                
                for func in expected_functions:
                    if func in content:
                        print(f"   ✓ _main.py provides {func}")
                        results['passed'] += 1
                    else:
                        print(f"   ❌ _main.py missing {func}")
                        results['failed'] += 1
                        results['errors'].append(f"_main.py missing: {func}")
                        
            except Exception as e:
                print(f"   ❌ Error validating _main.py: {e}")
                results['failed'] += 1
                results['errors'].append(f"_main.py validation error: {str(e)}")
        
        # Check __init__.py exports
        init_path = self.etabs_cwls_path / "__init__.py"
        if init_path.exists():
            try:
                with open(init_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if '__all__' in content and 'main' in content:
                    print("   ✓ __init__.py exports main functions")
                    results['passed'] += 1
                else:
                    print("   ❌ __init__.py missing proper exports")
                    results['failed'] += 1
                    results['errors'].append("__init__.py missing exports")
                    
            except Exception as e:
                print(f"   ❌ Error validating __init__.py: {e}")
                results['failed'] += 1
                results['errors'].append(f"__init__.py validation error: {str(e)}")
        
        return results
    
    def validate_api_consistency(self) -> Dict[str, Any]:
        """Validate API consistency across modules."""
        print("\n6. Validating API Consistency...")
        results = {'passed': 0, 'failed': 0, 'errors': []}
        
        # Check that all modules use consistent logging patterns
        logging_patterns = [
            'log_function_entry', 'log_function_exit', 'enhanced_log',
            'log_validation_result', 'log_error_with_context'
        ]
        
        for module in self.modules:
            file_path = self.etabs_cwls_path / f"{module}.py"
            if not file_path.exists():
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for logging imports
                if 'from .logging_config import' in content:
                    print(f"   ✓ {module}.py uses enhanced logging")
                    results['passed'] += 1
                else:
                    print(f"   ⚠️  {module}.py missing enhanced logging")
                
                # Check for exception handling
                if 'try:' in content and 'except' in content:
                    print(f"   ✓ {module}.py has exception handling")
                    results['passed'] += 1
                else:
                    print(f"   ⚠️  {module}.py missing exception handling")
                
                # Check for type hints
                if 'from typing import' in content:
                    print(f"   ✓ {module}.py uses type hints")
                    results['passed'] += 1
                else:
                    print(f"   ⚠️  {module}.py missing type hints")
                    
            except Exception as e:
                print(f"   ❌ Error validating {module}.py: {e}")
                results['failed'] += 1
                results['errors'].append(f"{module}.py validation error: {str(e)}")
        
        return results
    
    def print_summary(self):
        """Print validation summary."""
        print("\n" + "=" * 80)
        print("VALIDATION SUMMARY")
        print("=" * 80)
        
        total_passed = sum(result.get('passed', 0) for result in self.validation_results.values())
        total_failed = sum(result.get('failed', 0) for result in self.validation_results.values())
        total_tests = total_passed + total_failed
        
        print(f"Total Validations: {total_tests}")
        print(f"Passed: {total_passed} ✓")
        print(f"Failed: {total_failed} {'❌' if total_failed > 0 else '✓'}")
        print(f"Success Rate: {(total_passed/total_tests*100):.1f}%" if total_tests > 0 else "No tests run")
        
        # Print errors if any
        all_errors = []
        for result in self.validation_results.values():
            all_errors.extend(result.get('errors', []))
        
        if all_errors:
            print(f"\nErrors Found ({len(all_errors)}):")
            for i, error in enumerate(all_errors[:10], 1):  # Show first 10 errors
                print(f"  {i}. {error}")
            if len(all_errors) > 10:
                print(f"  ... and {len(all_errors) - 10} more errors")
        
        print("\n" + "=" * 80)
        if total_failed == 0:
            print("🎉 ALL VALIDATIONS PASSED! 🎉")
            print("The refactoring is successful and ready for use.")
        else:
            print("⚠️  SOME VALIDATIONS FAILED")
            print("Please review and fix the issues above.")
        print("=" * 80)

def main():
    """Run the validation."""
    validator = RefactoringValidator()
    results = validator.validate_all()
    
    # Return success/failure
    total_failed = sum(result.get('failed', 0) for result in results.values())
    return total_failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

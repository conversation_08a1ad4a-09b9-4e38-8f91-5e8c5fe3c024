"""Security and authentication management."""

import hashlib
import json
import logging
import os
import re
import secrets
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from config.app_config import (
    MAX_LOGIN_ATTEMPTS, LOGIN_TIMEOUT_MINUTES, SESSION_TOKEN_BYTES,
    SECURITY_LOG_PATH, EMAIL_DOMAIN
)


class SecurityManager:
    """Handles authentication, session management, and security logging."""
    
    def __init__(self):
        self.security_log_path = SECURITY_LOG_PATH
        self._ensure_security_log()
        
    def _ensure_security_log(self):
        """Initialize security log if it doesn't exist."""
        if not self.security_log_path.exists():
            self._write_log({"login_attempts": {}, "failed_logins": {}})
    
    def _read_log(self) -> Dict:
        """Read security log with error handling."""
        try:
            with open(self.security_log_path, 'r') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            logging.error("Security log corrupted or missing, reinitializing")
            default_data = {"login_attempts": {}, "failed_logins": {}}
            self._write_log(default_data)
            return default_data
    
    def _write_log(self, data: Dict):
        """Write to security log."""
        try:
            with open(self.security_log_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logging.error(f"Failed to write security log: {e}")
    
    def generate_session_token(self) -> str:
        """Generate secure session token."""
        return secrets.token_hex(SESSION_TOKEN_BYTES)
    
    def hash_password(self, password: str, salt: str) -> str:
        """Hash password with salt."""
        return hashlib.sha256((password + salt).encode()).hexdigest()
    
    def is_locked_out(self, username: str = None) -> bool:
        """Check if user or system is locked out."""
        data = self._read_log()
        
        # Check system-wide lockout
        if "_system_lockout" in data:
            lockout = data["_system_lockout"]
            last_attempt = datetime.fromisoformat(lockout["last_attempt"])
            if datetime.now() < last_attempt + timedelta(minutes=LOGIN_TIMEOUT_MINUTES):
                return True
            else:
                # Clear expired lockout
                data.pop("_system_lockout", None)
                self._write_log(data)
        
        # Check user-specific lockout
        if username and username in data.get("failed_logins", {}):
            user_data = data["failed_logins"][username]
            if user_data.get("count", 0) >= MAX_LOGIN_ATTEMPTS:
                last_attempt = datetime.fromisoformat(user_data["last_attempt"])
                if datetime.now() < last_attempt + timedelta(minutes=LOGIN_TIMEOUT_MINUTES):
                    return True
                else:
                    # Reset expired lockout
                    user_data["count"] = 0
                    self._write_log(data)
        
        return False
    
    def log_failed_attempt(self, username: str) -> bool:
        """Log failed login attempt. Returns True if account should be locked."""
        data = self._read_log()
        
        if "failed_logins" not in data:
            data["failed_logins"] = {}
        
        if username not in data["failed_logins"]:
            data["failed_logins"][username] = {
                "count": 1,
                "last_attempt": datetime.now().isoformat()
            }
        else:
            data["failed_logins"][username]["count"] += 1
            data["failed_logins"][username]["last_attempt"] = datetime.now().isoformat()
        
        # Create system lockout if max attempts reached
        if data["failed_logins"][username]["count"] >= MAX_LOGIN_ATTEMPTS:
            data["_system_lockout"] = {
                "username": username,
                "last_attempt": datetime.now().isoformat()
            }
        
        self._write_log(data)
        return data["failed_logins"][username]["count"] >= MAX_LOGIN_ATTEMPTS
    
    def log_successful_login(self, username: str, user_type: str, session_token: str):
        """Log successful login and clear failed attempts."""
        data = self._read_log()
        
        # Clear failed attempts
        if username in data.get("failed_logins", {}):
            data["failed_logins"][username]["count"] = 0
        
        # Log successful login
        if "login_attempts" not in data:
            data["login_attempts"] = {}
        
        data["login_attempts"][username] = {
            "last_login": datetime.now().isoformat(),
            "user_type": user_type,
            "session_token": session_token
        }
        
        self._write_log(data)
    
    def verify_session(self, username: str, session_token: str) -> bool:
        """Verify if session token is valid."""
        data = self._read_log()
        user_data = data.get("login_attempts", {}).get(username, {})
        return user_data.get("session_token") == session_token
    
    def get_user_email(self, username: str) -> str:
        """Get full email address for username."""
        return f"{username}{EMAIL_DOMAIN}"
    
    def get_authorized_users(self, url: str) -> List[str]:
        """Fetch authorized users from Google Drive."""
        # Log the incoming URL
        logging.info(f"get_authorized_users called with URL: {url}")
        
        try:
            # Extract file ID from various Google Drive URL formats
            file_id = None
            if '/d/' in url:
                file_id = url.split('/d/')[1].split('/')[0]
            elif 'id=' in url:
                file_id = url.split('id=')[1].split('&')[0]
            else:
                logging.error(f"Invalid Google Drive URL format: {url}")
                return []
            
            logging.info(f"Extracted file ID: {file_id}")

            # Try multiple download URLs
            download_urls = [
                f"https://drive.google.com/uc?export=download&id={file_id}",
                f"https://drive.google.com/uc?id={file_id}&export=download",
                f"https://docs.google.com/uc?export=download&id={file_id}"
            ]
            
            content = None
            
            for idx, direct_url in enumerate(download_urls):
                logging.info(f"Attempt {idx + 1}: Fetching from {direct_url}")
                
                # Method 1: Try using requests library
                try:
                    import requests
                    from config.ssl_config import configure_requests_ssl
                    
                    # Configure SSL
                    ssl_ok = configure_requests_ssl()
                    
                    # Set up session with headers to mimic browser
                    session = requests.Session()
                    session.headers.update({
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    })
                    
                    # Check for proxy settings
                    proxies = {}
                    if os.getenv('HTTP_PROXY'):
                        proxies['http'] = os.getenv('HTTP_PROXY')
                    if os.getenv('HTTPS_PROXY'):
                        proxies['https'] = os.getenv('HTTPS_PROXY')
                    
                    # Make request
                    if ssl_ok:
                        response = session.get(direct_url, timeout=15, proxies=proxies if proxies else None, allow_redirects=True)
                    else:
                        response = session.get(direct_url, timeout=15, verify=False, proxies=proxies if proxies else None, allow_redirects=True)
                    
                    logging.info(f"Response {response.status_code}, Content-Type: {response.headers.get('content-type', 'unknown')}")
                    
                    if response.status_code == 200:
                        content = response.text
                        
                        # Check if we got a virus scan warning page
                        if 'virus scan warning' in content.lower() or 'google drive - virus scan warning' in content.lower():
                            logging.warning("Got virus scan warning page, trying to extract download link")
                            # Try to extract the confirm token and download again
                            confirm_match = re.search(r'confirm=([0-9A-Za-z_-]+)', content)
                            if confirm_match:
                                confirm_token = confirm_match.group(1)
                                confirmed_url = f"{direct_url}&confirm={confirm_token}"
                                logging.info(f"Retrying with confirm token: {confirmed_url}")
                                response = session.get(confirmed_url, timeout=15, verify=not ssl_ok, proxies=proxies if proxies else None)
                                if response.status_code == 200:
                                    content = response.text
                        
                        # Check if content is valid (not HTML error page)
                        if content and not ('<html' in content.lower()[:100] or '<!doctype' in content.lower()[:100]):
                            logging.info(f"Successfully fetched content. Length: {len(content)}")
                            break
                        elif '<html' in content.lower()[:100]:
                            logging.warning("Received HTML page instead of user list")
                            content = None
                            
                except ImportError:
                    logging.warning("Requests library not available")
                except Exception as e:
                    logging.warning(f"Request attempt {idx + 1} failed: {type(e).__name__}: {e}")
                
            if not content:
                logging.error("Failed to fetch content from any method")
                return []
            
            # Check if we got an HTML error page
            if '<html' in content.lower() or '<!doctype' in content.lower():
                logging.error("Received HTML content instead of user list - possible authentication or access issue")
                logging.debug(f"HTML content (first 500 chars): {content[:500]}")
                return []
            
            # Log content for debugging
            logging.info(f"Content type appears to be: {'JSON' if content.strip().startswith('[') else 'Plain text'}")
            logging.debug(f"Full content (first 500 chars): {repr(content[:500])}")
            
            # Parse users - try multiple patterns
            users = []
            
            # Clean content - remove BOM if present
            content = content.lstrip('\ufeff')
            
            # Check if content is JSON array
            if content.strip().startswith('['):
                try:
                    import json
                    user_list = json.loads(content)
                    if isinstance(user_list, list):
                        users = [str(u).strip().lower() for u in user_list if u]
                        logging.info(f"Parsed JSON array with {len(users)} users")
                except json.JSONDecodeError as e:
                    logging.error(f"Failed to parse JSON: {e}")
            
            # If not JSON, try other patterns
            if not users:
                # Remove any HTML tags if present
                content = re.sub(r'<[^>]+>', '', content)
                
                # Pattern 1: Users in quotes with possible whitespace
                all_quoted = re.findall(r'["\']([^"\']+)["\']', content)
                for item in all_quoted:
                    item = item.strip().lower()
                    # Validate username format
                    if re.match(r'^[a-z]+\.[a-z]+(?:\.[a-z]+)?$', item):
                        users.append(item)
                
                # Pattern 2: Plain text, one per line
                if not users:
                    lines = content.split('\n')
                    for line in lines:
                        line = line.strip().lower()
                        if line and '.' in line and not line.startswith('#'):
                            # Clean common separators
                            line = line.replace(',', '').replace(';', '').strip()
                            if re.match(r'^[a-z]+\.[a-z]+(?:\.[a-z]+)?$', line):
                                users.append(line)
            
            # Remove duplicates
            users = list(set(users))
            
            logging.info(f"Total unique users found: {len(users)}")
            if users:
                logging.info(f"First 10 users: {users[:10]}")
            else:
                logging.warning("No valid users found in content")
                logging.debug(f"Raw content (first 1000 chars): {repr(content[:1000])}")
            
            return users
            
        except Exception as e:
            logging.error(f"Critical error in get_authorized_users: {type(e).__name__}: {e}")
            import traceback
            logging.error(traceback.format_exc())
            return []

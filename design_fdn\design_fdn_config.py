"""
Configuration file for design_fdn module.
Contains file path constants and Excel sheet name constants.
"""

# =============================================================================
# INPUT FILE NAME CONSTANTS
# =============================================================================

# Input Excel Files (same as build_fem for consistency)
EXCEL_PROPERTY_FILENAME = 'A.SAFEInput_Property.xlsx'
EXCEL_GEOMETRY_FILENAME = 'A.SAFEInput_Geometry.xlsx'
EXCEL_GEOLOGY_FILENAME = 'A.SAFEInput_Geology.xlsx'
EXCEL_LOADING_FILENAME = 'A.SAFEInput_Loading.xlsx'

# Input Database Files
SAFE_RESULT_DATABASE_FILENAME = '*.mdb'  # SAFE result database file

# Steel Section Library
STEEL_SECTION_FILENAME = 'SteelSection.xlsx'

# =============================================================================
# OUTPUT FILE NAME CONSTANTS - MAIN RESULTS
# =============================================================================

# Main Result Files
DESIGN_LOG_FILENAME = '!Design_Log.txt'
DESIGN_LOAD_FILENAME = 'R1.Design_Load(Stepping_Effect).xlsx'
PILE_CAPACITY_FILENAME = 'R2.Pile_Capacity.xlsx'
PILING_SCHEDULE_FILENAME = 'R3.Pile_Loading_Schedule.xlsx'

# =============================================================================
# OUTPUT FILE NAME CONSTANTS - ULS RESULTS
# =============================================================================

# ULS SQLite Database Files
PILE_ULS_SQLITE_FILENAME = 'R0.ElementForcesColumnsAndBraces(ULS).sqlite'
PILE_SLS_SQLITE_FILENAME = 'R0.ElementForcesColumnsAndBraces(SLS).sqlite'
PILE_ULS_PDELTA_SQLITE_FILENAME = 'R0.ElementForcesColumnsAndBraces(ULS_PDelta).sqlite'
PILE_ULS_PDELTA_CAL_SQLITE_FILENAME = 'R0.ULS_PDelta_Calculations.sqlite'
PILE_PDELTA_CSV_FILENAME = 'R0.ElementForcesColumnsAndBraces(PDelta).csv'
STEEL_HPILE_CHECK_BD_SQLITE_FILENAME = 'R0.ResultSteelHPileCheckBD.sqlite'
STEEL_HPILE_CHECK_ASD_SQLITE_FILENAME = 'R0.ResultSteelHPileCheckASD.sqlite'

# ULS Result Files
PILE_NEEDS_PDELTA_FILENAME = 'R1.PilesNeedPDelta.csv'

# BP (Bored Pile) Result Files
BP_SHEAR_CHECK_FILENAME = 'BP.Shear_Check.xlsx'
BP_SHEAR_DESIGN_FILENAME = 'BP.Shear_Design.xlsx'
BP_SEGMENT_REBAR_LOG_FILENAME = 'BP.Pile_NM_Design_Summary (Segment).xlsx'
BP_REBAR_LOG_FILENAME = 'BP.Pile_NM_Design_Summary (Combined).xlsx'

# Steel H-Pile Result Files
SHP_CHECK_FILENAME = 'SHP.Combined_Stress_Check.xlsx'
DHP_CHECK_FILENAME = 'DHP.Combined_Stress_Check.xlsx'

# =============================================================================
# OUTPUT FILE NAME CONSTANTS - SLS RESULTS
# =============================================================================

# SLS SQLite Database Files
PILE_LOCAL_XY_SQLITE_FILENAME = 'R0.PileNodalDisplacement(Local).sqlite'
SETTLEMENT_SQLITE_FILENAME = 'R0.Settlement(SLS).sqlite'
LATERAL_DISPLACEMENT_SQLITE_FILENAME = 'R0.Displacement(Lateral Load).sqlite'

# SLS Result Files
PILE_DEFLECTION_CHECK_FILENAME = 'R1.PileDeflectionsCheck.csv'
PILE_DEFLECTION_FAIL_FILENAME = 'R1.PileDeflectionsCheck(Fail).csv'
ANGULAR_ROTATION_CHECK_FILENAME = 'R2.AngularRotationCheck.csv'
ANGULAR_ROTATION_FAIL_FILENAME = 'R2.AngularRotationCheck(Fail).csv'
DIFFERENTIAL_SETTLEMENT_CHECK_FILENAME = 'R3.DifferentialSettlementCheck.xlsx'
DIFFERENTIAL_SETTLEMENT_FAIL_FILENAME = 'R3.DifferentialSettlementCheck(Fail).xlsx'

# =============================================================================
# EXCEL SHEET NAME CONSTANTS - BP (BORED PILE) RESULTS
# =============================================================================

# BP Shear Design Sheets
SHEET_BP_SHEAR_CHECK = 'BP_Shear_Check'
SHEET_BP_SHEAR_DESIGN = 'BP_Shear_Design'
SHEET_BP_ERROR_LOG = 'Error_Log'

# BP NM Design Sheets
SHEET_BP_LOAD = 'Load'
SHEET_BP_FAILED_LOAD = 'Failed_Load'
SHEET_BP_NM_CURVE = 'NM_Curve'
SHEET_BP_REBAR_COORD = 'Rebar_Coord'
SHEET_BP_SEGMENT_REBAR = 'BP_Segment_Rebar'

# =============================================================================
# EXCEL SHEET NAME CONSTANTS - STEEL H-PILE RESULTS
# =============================================================================

# SHP (Socketed Steel H-Pile) Sheets
SHEET_SHP_DESIGN_MXY_BD = 'SHPDesignMxy (BD)'
SHEET_SHP_DESIGN_VX_BD = 'SHPDesignVx (BD)'
SHEET_SHP_DESIGN_VY_BD = 'SHPDesignVy (BD)'
SHEET_SHP_DESIGN_MXY_ASD_GRAVITY = 'SHPDesignMxy (ASD Gravity)'
SHEET_SHP_DESIGN_VX_ASD_GRAVITY = 'SHPDesignVx (ASD Gravity)'
SHEET_SHP_DESIGN_VY_ASD_GRAVITY = 'SHPDesignVy (ASD Gravity)'
SHEET_SHP_DESIGN_MXY_ASD_WIND = 'SHPDesignMxy (ASD Wind)'
SHEET_SHP_DESIGN_VX_ASD_WIND = 'SHPDesignVx (ASD Wind)'
SHEET_SHP_DESIGN_VY_ASD_WIND = 'SHPDesignVy (ASD Wind)'

# DHP (Driven Steel H-Pile) Sheets
SHEET_DHP_DESIGN_MXY_BD = 'DHPDesignMxy (BD)'
SHEET_DHP_DESIGN_VX_BD = 'DHPDesignVx (BD)'
SHEET_DHP_DESIGN_VY_BD = 'DHPDesignVy (BD)'
SHEET_DHP_DESIGN_MXY_ASD_GRAVITY = 'DHPDesignMxy (ASD Gravity)'
SHEET_DHP_DESIGN_VX_ASD_GRAVITY = 'DHPDesignVx (ASD Gravity)'
SHEET_DHP_DESIGN_VY_ASD_GRAVITY = 'DHPDesignVy (ASD Gravity)'
SHEET_DHP_DESIGN_MXY_ASD_WIND = 'DHPDesignMxy (ASD Wind)'
SHEET_DHP_DESIGN_VX_ASD_WIND = 'DHPDesignVx (ASD Wind)'
SHEET_DHP_DESIGN_VY_ASD_WIND = 'DHPDesignVy (ASD Wind)'

# =============================================================================
# EXCEL SHEET NAME CONSTANTS - SLS RESULTS
# =============================================================================

# Differential Settlement Sheets
SHEET_VE_DSETT_FAIL = 'VE_DSett_Fail'
SHEET_PILE_DSETT_FAIL = 'Pile_DSett_Fail'
SHEET_VE_DSETT_CHECK = 'VE_DSett_Check'
SHEET_VE_DSETT = 'VE_DSett'
SHEET_VE_DS = 'VE_dS'
SHEET_VE_DISTANCE = 'VE_Distance'
SHEET_PILE_DSETT_CHECK = 'Pile_DSett_Check'
SHEET_PILE_DSETT = 'Pile_DSett'
SHEET_PILE_DS = 'Pile_dS'
SHEET_PILE_DISTANCE = 'Pile_Distance'

# Other Result Sheets
SHEET_PILE_CAPACITY = 'PileCapacity'
SHEET_BP_REBAR = 'BP_Rebar'
SHEET_LOAD = 'Load'

# =============================================================================
# EXCEL SHEET NAME CONSTANTS - PILE CAPACITY RESULTS
# =============================================================================

# Pile Type Sheets in Capacity File
SHEET_BP_CAPACITY = 'BP'
SHEET_SHP_CAPACITY = 'SHP'
SHEET_DHP_CAPACITY = 'DHP'
SHEET_MP_CAPACITY = 'MP'

# Piling Schedule Sheet
SHEET_PILING_SCHEDULE = 'Piling Schedule'

# =============================================================================
# FOLDER NAME CONSTANTS
# =============================================================================

# Main Output Folders
ANALYTICAL_RESULTS_FOLDER = 'Analytical_Results'
ULS_FOLDER = 'ULS'
SLS_FOLDER = 'SLS'

# BP Specific Folders
BP_PROKON_FOLDER = 'BP.Prokon'
BP_NM_DESIGN_DETAIL_FOLDER = 'BP.NM_Design (Detail)'

# =============================================================================
# DATABASE TABLE NAME CONSTANTS
# =============================================================================

# SQLite Table Names
TABLE_PILE_ULS = 'PileULS'
TABLE_PILE_SLS = 'PileSLS'
TABLE_PILE_LOCAL_XY = 'ExcelOutputPileLocalXY'
TABLE_SETTLEMENT = 'ExcelOutputSettlement'
TABLE_LATERAL_DISPLACEMENT = 'ExcelOutputLateralDisplacement'
TABLE_STEEL_HPILE_CHECK_BD = 'ResultSteelHPileCheckBD'
TABLE_STEEL_HPILE_CHECK_ASD = 'ResultSteelHPileCheckASD'

"""
Custom Exception Classes for ETABS CWLS Module

This module defines custom exception classes for the ETABS Core Wall Loading Schedule
module, providing specific error types for different failure scenarios and enabling
better error handling and debugging.

The exception hierarchy follows professional Python practices and provides detailed
error information for troubleshooting and user feedback.
"""

from typing import Optional, Any, Dict


class ETABSCWLSError(Exception):
    """
    Base exception class for all ETABS CWLS module errors.
    
    This is the base class for all custom exceptions in the ETABS CWLS module.
    It provides common functionality for error handling and logging.
    
    Attributes:
        message: Human-readable error message
        error_code: Optional error code for programmatic handling
        context: Optional dictionary containing error context information
    """
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None, 
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Initialize the base ETABS CWLS error.
        
        Args:
            message: Human-readable error message
            error_code: Optional error code for programmatic handling
            context: Optional dictionary containing error context information
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.context = context or {}
    
    def __str__(self) -> str:
        """Return string representation of the error."""
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
    
    def get_context(self) -> Dict[str, Any]:
        """Get error context information."""
        return self.context.copy()


class FileOperationError(ETABSCWLSError):
    """
    Exception raised for file operation errors.
    
    This exception is raised when file operations fail, such as:
    - File not found
    - Permission denied
    - Invalid file format
    - File corruption
    """
    
    def __init__(
        self, 
        message: str, 
        file_path: Optional[str] = None, 
        operation: Optional[str] = None,
        error_code: Optional[str] = None
    ) -> None:
        """
        Initialize file operation error.
        
        Args:
            message: Human-readable error message
            file_path: Path to the file that caused the error
            operation: Type of operation that failed (read, write, delete, etc.)
            error_code: Optional error code
        """
        context = {}
        if file_path:
            context['file_path'] = file_path
        if operation:
            context['operation'] = operation
            
        super().__init__(message, error_code, context)
        self.file_path = file_path
        self.operation = operation


class DataValidationError(ETABSCWLSError):
    """
    Exception raised for data validation errors.
    
    This exception is raised when input data fails validation, such as:
    - Invalid data types
    - Missing required fields
    - Data out of acceptable range
    - Inconsistent data relationships
    """
    
    def __init__(
        self, 
        message: str, 
        field_name: Optional[str] = None, 
        invalid_value: Optional[Any] = None,
        expected_type: Optional[str] = None,
        error_code: Optional[str] = None
    ) -> None:
        """
        Initialize data validation error.
        
        Args:
            message: Human-readable error message
            field_name: Name of the field that failed validation
            invalid_value: The invalid value that caused the error
            expected_type: Expected data type or format
            error_code: Optional error code
        """
        context = {}
        if field_name:
            context['field_name'] = field_name
        if invalid_value is not None:
            context['invalid_value'] = str(invalid_value)
        if expected_type:
            context['expected_type'] = expected_type
            
        super().__init__(message, error_code, context)
        self.field_name = field_name
        self.invalid_value = invalid_value
        self.expected_type = expected_type


class ETABSConnectionError(ETABSCWLSError):
    """
    Exception raised for ETABS connection and data access errors.
    
    This exception is raised when there are issues connecting to or reading
    from ETABS database files, such as:
    - Database connection failures
    - Missing tables or columns
    - Data format incompatibilities
    - Version compatibility issues
    """
    
    def __init__(
        self, 
        message: str, 
        database_path: Optional[str] = None, 
        table_name: Optional[str] = None,
        etabs_version: Optional[str] = None,
        error_code: Optional[str] = None
    ) -> None:
        """
        Initialize ETABS connection error.
        
        Args:
            message: Human-readable error message
            database_path: Path to the ETABS database file
            table_name: Name of the table that caused the error
            etabs_version: ETABS version information
            error_code: Optional error code
        """
        context = {}
        if database_path:
            context['database_path'] = database_path
        if table_name:
            context['table_name'] = table_name
        if etabs_version:
            context['etabs_version'] = etabs_version
            
        super().__init__(message, error_code, context)
        self.database_path = database_path
        self.table_name = table_name
        self.etabs_version = etabs_version


class CalculationError(ETABSCWLSError):
    """
    Exception raised for calculation and processing errors.
    
    This exception is raised when calculations fail or produce invalid results, such as:
    - Mathematical errors (division by zero, invalid operations)
    - Convergence failures
    - Invalid calculation parameters
    - Numerical instabilities
    """
    
    def __init__(
        self, 
        message: str, 
        calculation_type: Optional[str] = None, 
        input_parameters: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None
    ) -> None:
        """
        Initialize calculation error.
        
        Args:
            message: Human-readable error message
            calculation_type: Type of calculation that failed
            input_parameters: Input parameters that caused the error
            error_code: Optional error code
        """
        context = {}
        if calculation_type:
            context['calculation_type'] = calculation_type
        if input_parameters:
            context['input_parameters'] = input_parameters
            
        super().__init__(message, error_code, context)
        self.calculation_type = calculation_type
        self.input_parameters = input_parameters


class ConfigurationError(ETABSCWLSError):
    """
    Exception raised for configuration and setup errors.
    
    This exception is raised when there are configuration issues, such as:
    - Missing configuration files
    - Invalid configuration values
    - Incompatible settings
    - Environment setup problems
    """
    
    def __init__(
        self, 
        message: str, 
        config_key: Optional[str] = None, 
        config_value: Optional[Any] = None,
        error_code: Optional[str] = None
    ) -> None:
        """
        Initialize configuration error.
        
        Args:
            message: Human-readable error message
            config_key: Configuration key that caused the error
            config_value: Invalid configuration value
            error_code: Optional error code
        """
        context = {}
        if config_key:
            context['config_key'] = config_key
        if config_value is not None:
            context['config_value'] = str(config_value)
            
        super().__init__(message, error_code, context)
        self.config_key = config_key
        self.config_value = config_value


class GUIError(ETABSCWLSError):
    """
    Exception raised for GUI-related errors.
    
    This exception is raised when there are GUI operation failures, such as:
    - Widget creation failures
    - Event handling errors
    - Display issues
    - User interaction problems
    """
    
    def __init__(
        self, 
        message: str, 
        widget_type: Optional[str] = None, 
        operation: Optional[str] = None,
        error_code: Optional[str] = None
    ) -> None:
        """
        Initialize GUI error.
        
        Args:
            message: Human-readable error message
            widget_type: Type of widget that caused the error
            operation: GUI operation that failed
            error_code: Optional error code
        """
        context = {}
        if widget_type:
            context['widget_type'] = widget_type
        if operation:
            context['operation'] = operation
            
        super().__init__(message, error_code, context)
        self.widget_type = widget_type
        self.operation = operation


# Export all exception classes
__all__ = [
    'ETABSCWLSError',
    'FileOperationError',
    'DataValidationError',
    'ETABSConnectionError',
    'CalculationError',
    'ConfigurationError',
    'GUIError'
]

"""
Analysis plotting functions for DXF visualization.

This module contains functions for plotting preselection analysis data
and possible pile positions.
"""

from typing import List, Dict, Any, Optional
from ezdxf.enums import TextEntityAlignment

from ..data_types import Point2D
from .text_manager import TextPlacementManager
from .utils import safe_get


def plot_preselection_analysis_dxf(msp, group_results: Dict[str, Any], text_manager: TextPlacementManager, log_callback=None) -> None:
    """
    Plot preselection analysis data for all groups.
    
    This includes:
    - Optimal rectangles
    - Initial pile caps  
    - Individual preselection cap layers per pile type
    - Pile type positions with proper BP naming (capacity + diameter)
    
    Args:
        msp: DXF model space
        group_results: Dictionary of group results containing preselection data
        text_manager: TextPlacementManager for label positioning
        log_callback: Logging callback
    """
    try:
        if log_callback:
            log_callback("Plotting preselection analysis data...")
        
        groups_with_preselection = 0
        total_preselection_positions = 0
        
        for group_name, group_data in group_results.items():
            preselection_analysis = _extract_preselection_analysis(group_data, group_name, log_callback)
            
            if not preselection_analysis:
                if log_callback:
                    log_callback(f"   No preselection analysis found for group {group_name}")
                continue
                
            groups_with_preselection += 1
            
            # Plot optimal rectangle
            _plot_optimal_rectangle(msp, preselection_analysis, group_name, text_manager, log_callback)
            
            # Plot initial pile cap
            _plot_initial_pile_cap(msp, preselection_analysis, group_name, log_callback)
            
            # Plot pile type boundaries and positions
            positions_plotted = _plot_pile_type_data(
                msp, preselection_analysis, group_data, text_manager, log_callback
            )
            total_preselection_positions += positions_plotted
        
        if log_callback:
            log_callback(f"Preselection analysis plotting completed:")
            log_callback(f"   Groups with preselection: {groups_with_preselection}/{len(group_results)}")
            log_callback(f"   Total preselection positions plotted: {total_preselection_positions}")
            
    except Exception as e:
        if log_callback:
            log_callback(f"Error plotting preselection analysis: {e}")
        import traceback
        if log_callback:
            log_callback(f"Traceback: {traceback.format_exc()}")
        traceback.print_exc()


def _extract_preselection_analysis(group_data: Any, group_name: str, log_callback=None) -> Optional[Dict]:
    """Extract preselection analysis from group data using multiple methods."""
    preselection_analysis = None
    
    # Check if it's directly available as attribute
    if hasattr(group_data, 'preselection_analysis'):
        preselection_analysis = group_data.preselection_analysis
        if log_callback:
            log_callback(f"   Processing group {group_name}...")
            log_callback(f"      Found preselection_analysis as attribute")
    
    # Check if it's in a dictionary format
    if preselection_analysis is None and isinstance(group_data, dict):
        preselection_analysis = group_data.get('preselection_analysis', {})
        if preselection_analysis and log_callback:
            log_callback(f"   Processing group {group_name}...")
            log_callback(f"      Found preselection_analysis in dict format")
    
    # Check safe_get method
    if not preselection_analysis:
        preselection_analysis = safe_get(group_data, 'preselection_analysis', {})
        if preselection_analysis and log_callback:
            log_callback(f"   Processing group {group_name}...")
            log_callback(f"      Found preselection_analysis via safe_get")
    
    return preselection_analysis if preselection_analysis else None


def _plot_optimal_rectangle(msp, preselection_analysis: Dict, group_name: str, 
                           text_manager: TextPlacementManager, log_callback=None) -> None:
    """Plot optimal rectangle for the group."""
    local_system = preselection_analysis.get('local_coordinate_system')
    if local_system:
        try:
            from ..utils.coordinate_utils import visualize_optimal_rectangle_in_dxf
            visualize_optimal_rectangle_in_dxf(msp, local_system, 'OPTIMAL_RECTANGLE')
            
            # Add dimension labels
            center = local_system.origin
            rect_label = f"Group {group_name} Optimal Rectangle\n{local_system.long_axis_length:.2f}m {local_system.short_axis_length:.2f}m"
            
            final_x, final_y = text_manager.add_text_position(center[0], center[1] - 1.0, rect_label, 0.25)
            text = msp.add_text(
                rect_label,
                dxfattribs={'layer': 'TEXT', 'height': 0.25}
            )
            text.set_placement((final_x, final_y), align=TextEntityAlignment.MIDDLE_CENTER)
            
            if log_callback:
                log_callback(f"      Optimal rectangle plotted for group {group_name}")
        except Exception as e:
            if log_callback:
                log_callback(f"      Failed to plot optimal rectangle: {e}")
    else:
        if log_callback:
            log_callback(f"      No local coordinate system found for group {group_name}")


def _plot_initial_pile_cap(msp, preselection_analysis: Dict, group_name: str, log_callback=None) -> None:
    """Plot initial pile cap for the group."""
    initial_pile_cap = preselection_analysis.get('initial_pile_cap')
    if initial_pile_cap and not initial_pile_cap.is_empty:
        try:
            coords = list(initial_pile_cap.exterior.coords)
            if len(coords) >= 3:
                polyline = msp.add_lwpolyline(coords[:-1])
                polyline.close()
                polyline.dxf.layer = 'INITIAL_PILE_CAPS'
                
                if log_callback:
                    log_callback(f"      Initial pile cap plotted for group {group_name}")
        except Exception as e:
            if log_callback:
                log_callback(f"      Failed to plot initial pile cap: {e}")
    else:
        if log_callback:
            log_callback(f"      No valid initial pile cap found for group {group_name}")


def _plot_pile_type_data(msp, preselection_analysis: Dict, group_data: Any, 
                        text_manager: TextPlacementManager, log_callback=None) -> int:
    """Plot pile type boundaries and positions. Returns number of positions plotted."""
    all_pile_type_positions = preselection_analysis.get('all_pile_type_positions', {})
    all_possible_pile_boundaries = preselection_analysis.get('all_possible_pile_boundaries', {})
    selected_pile_type = preselection_analysis.get('selected_pile_type')
    evaluation_summary = preselection_analysis.get('evaluation_summary', {})
    
    # If no positions found in direct location, check evaluation_summary
    if not all_pile_type_positions and evaluation_summary:
        if log_callback:
            log_callback(f"      Checking evaluation_summary for pile positions: {list(evaluation_summary.keys())}")
        
        for pile_type_name, eval_data in evaluation_summary.items():
            if isinstance(eval_data, dict) and 'grid_positions' in eval_data:
                all_pile_type_positions[pile_type_name] = eval_data['grid_positions']
                if log_callback:
                    log_callback(f"      Found {len(eval_data['grid_positions'])} positions for {pile_type_name} in evaluation_summary")
    
    # Plot pile type boundaries
    _plot_pile_boundaries(msp, all_possible_pile_boundaries, group_data, preselection_analysis, 
                         evaluation_summary, selected_pile_type, text_manager, log_callback)
    
    # Plot pile type positions
    total_positions = _plot_pile_positions(msp, all_pile_type_positions, group_data, preselection_analysis,
                                          evaluation_summary, selected_pile_type, log_callback)
    
    return total_positions


def _plot_pile_boundaries(msp, all_possible_pile_boundaries: Dict, group_data: Any,
                         preselection_analysis: Dict, evaluation_summary: Dict,
                         selected_pile_type: Any, text_manager: TextPlacementManager,
                         log_callback=None) -> None:
    """Plot pile type boundaries (maximum pile caps)."""
    if not all_possible_pile_boundaries:
        if log_callback:
            log_callback(f"      No pile type boundaries found")
        return

    # Import the helper function for creating BP layers
    from ..visualizer.dxf_setup import create_bp_layers_if_needed

    for pile_type_name, boundary in all_possible_pile_boundaries.items():
        if boundary and not boundary.is_empty:
            try:
                coords = list(boundary.exterior.coords)
                if len(coords) >= 3:
                    # Create appropriate cap layer name
                    if pile_type_name == 'BP':
                        capacity, diameter = _extract_bp_specs(
                            group_data, preselection_analysis, evaluation_summary, selected_pile_type
                        )

                        if capacity is not None and diameter is not None:
                            capacity_int = int(capacity)
                            diameter_str = f"{diameter:.0f}" if diameter == int(diameter) else f"{diameter:.1f}"
                            boundary_layer = f'PRESELECTION_BP_{capacity_int}kN_{diameter_str}m_CAP'
                            # Create layer if needed
                            create_bp_layers_if_needed(msp.doc, capacity, diameter, log_callback)
                        else:
                            boundary_layer = f'PRESELECTION_{pile_type_name}_CAP'
                            if log_callback:
                                log_callback(f"      Warning: Missing capacity or diameter for BP boundary layer")
                    else:
                        boundary_layer = f'PRESELECTION_{pile_type_name}_CAP'

                    polyline = msp.add_lwpolyline(coords[:-1])
                    polyline.close()
                    polyline.dxf.layer = boundary_layer

                    # Add boundary label
                    centroid = boundary.centroid
                    boundary_label = f"Max Cap {pile_type_name}"
                    final_x, final_y = text_manager.add_text_position(
                        centroid.x, centroid.y - 0.5, boundary_label, 0.2
                    )
                    text = msp.add_text(
                        boundary_label,
                        dxfattribs={'layer': 'TEXT', 'height': 0.2}
                    )
                    text.set_placement((final_x, final_y), align=TextEntityAlignment.MIDDLE_CENTER)

                    if log_callback:
                        log_callback(f"      {pile_type_name} maximum pile cap boundary plotted to layer {boundary_layer}")
            except Exception as e:
                if log_callback:
                    log_callback(f"      Failed to plot {pile_type_name} boundary: {e}")


def _plot_pile_positions(msp, all_pile_type_positions: Dict, group_data: Any,
                        preselection_analysis: Dict, evaluation_summary: Dict,
                        selected_pile_type: Any, log_callback=None) -> int:
    """Plot pile type positions. Returns total number of positions plotted."""
    if log_callback:
        log_callback(f"      All pile type positions keys: {list(all_pile_type_positions.keys())}")
        for pile_type_name, positions in all_pile_type_positions.items():
            log_callback(f"      {pile_type_name}: {len(positions) if positions else 0} positions")

    total_positions = 0

    # Import the helper function for creating BP layers
    from ..visualizer.dxf_setup import create_bp_layers_if_needed

    for pile_type_name, positions in all_pile_type_positions.items():
        if not positions:
            if log_callback:
                log_callback(f"      No positions for {pile_type_name}")
            continue

        # For BP types, create individual layers based on capacity and diameter
        if pile_type_name == 'BP':
            capacity, diameter = _extract_bp_specs(
                group_data, preselection_analysis, evaluation_summary, selected_pile_type
            )

            if capacity is not None and diameter is not None:
                layer_name = create_bp_layers_if_needed(msp.doc, capacity, diameter, log_callback)
            else:
                layer_name = f'PRESELECTION_{pile_type_name}'
                if log_callback:
                    log_callback(f"      Warning: Missing capacity or diameter for BP layer")
        else:
            layer_name = f'PRESELECTION_{pile_type_name}'

        is_selected = (selected_pile_type and
                      hasattr(selected_pile_type, 'pile_type') and
                      selected_pile_type.pile_type.name == pile_type_name)

        plotted_count = 0
        for pos in positions:
            try:
                x, y = float(pos[0]), float(pos[1])

                # ALL preselection positions use cross markers as per Pile_Layout_Rules.md
                marker_size = 0.15 if is_selected else 0.1

                # Draw cross marker for all pile types
                msp.add_line((x - marker_size, y), (x + marker_size, y),
                           dxfattribs={'layer': layer_name})
                msp.add_line((x, y - marker_size), (x, y + marker_size),
                           dxfattribs={'layer': layer_name})

                plotted_count += 1
                total_positions += 1
            except Exception as pos_error:
                if log_callback:
                    log_callback(f"      Error plotting position {pos}: {pos_error}")

        if log_callback:
            log_callback(f"      {pile_type_name}: {plotted_count}/{len(positions)} preselection positions plotted to layer {layer_name}")

    return total_positions


def _extract_bp_specs(group_data: Any, preselection_analysis: Dict, evaluation_summary: Dict,
                     selected_pile_type: Any) -> tuple:
    """Extract BP capacity and diameter from multiple sources. Returns (capacity, diameter)."""
    capacity = None
    diameter = None

    # PRIORITY 1: Try to get from group result's selected_pile_spec
    if group_data and hasattr(group_data, 'selected_pile_spec') and group_data.selected_pile_spec:
        pile_spec = group_data.selected_pile_spec
        if (hasattr(pile_spec, 'pile_type') and
            hasattr(pile_spec.pile_type, 'name') and
            pile_spec.pile_type.name == 'BP'):
            if hasattr(pile_spec, 'capacity'):
                capacity = pile_spec.capacity
            if hasattr(pile_spec, 'diameter'):
                diameter = pile_spec.diameter

    # PRIORITY 2: Try to get BP info from evaluation summary
    if (capacity is None or diameter is None) and evaluation_summary and 'BP' in evaluation_summary:
        bp_eval_data = evaluation_summary['BP']
        if isinstance(bp_eval_data, dict):
            if capacity is None:
                capacity = bp_eval_data.get('capacity_per_pile')
            if diameter is None:
                diameter = bp_eval_data.get('diameter')

    # PRIORITY 3: Try selected pile type (if it's BP)
    if (capacity is None or diameter is None) and selected_pile_type:
        if (hasattr(selected_pile_type, 'pile_type') and
            hasattr(selected_pile_type.pile_type, 'name') and
            selected_pile_type.pile_type.name == 'BP'):
            if capacity is None and hasattr(selected_pile_type, 'capacity_per_pile'):
                capacity = selected_pile_type.capacity_per_pile
            if diameter is None and hasattr(selected_pile_type, 'diameter'):
                diameter = selected_pile_type.diameter

    # PRIORITY 4: Try to extract from any available pile candidates
    if capacity is None or diameter is None:
        all_candidates = preselection_analysis.get('all_pile_candidates', [])
        for candidate in all_candidates:
            if (hasattr(candidate, 'pile_type') and
                hasattr(candidate.pile_type, 'name') and
                candidate.pile_type.name == 'BP'):
                if capacity is None and hasattr(candidate, 'capacity_per_pile'):
                    capacity = candidate.capacity_per_pile
                if diameter is None and hasattr(candidate, 'diameter'):
                    diameter = candidate.diameter
                break

    return capacity, diameter


def plot_possible_pile_positions_dxf(msp, possible_positions: List[Point2D], pile_diameter: Optional[float],
                                   text_manager: TextPlacementManager, log_callback=None) -> None:
    """
    Plot possible pile positions with different visual representation.

    Args:
        msp: Model space
        possible_positions: List of possible pile positions
        pile_diameter: Pile diameter for sizing
        text_manager: Text placement manager
        log_callback: Logging callback function
    """
    try:
        if not possible_positions:
            if log_callback:
                log_callback("    No possible pile positions to plot")
            return

        if log_callback:
            log_callback(f"    Plotting {len(possible_positions)} possible pile positions")

        successfully_plotted = 0

        for i, pos in enumerate(possible_positions):
            try:
                if len(pos) >= 2:
                    x, y = float(pos[0]), float(pos[1])

                    # Validate coordinates are reasonable
                    if abs(x) > 10000 or abs(y) > 10000:
                        if log_callback:
                            log_callback(f"      Possible position {i+1} has unreasonable coordinates: ({x}, {y})")
                        continue

                    # Add possible position marker (smaller circle with dashed line)
                    radius = (pile_diameter or 0.6) / 4.0  # Smaller radius for possible positions

                    # Create a small circle for possible position
                    circle = msp.add_circle((x, y), radius)
                    circle.dxf.layer = 'POSSIBLE_POSITIONS'
                    circle.dxf.color = 8  # Gray color for possible positions
                    circle.dxf.linetype = 'DASHED'  # Dashed line style

                    # Add small cross marker at center
                    cross_size = radius * 0.5
                    # Horizontal line
                    msp.add_line(
                        (x - cross_size, y),
                        (x + cross_size, y),
                        dxfattribs={'layer': 'POSSIBLE_POSITIONS', 'color': 8}
                    )
                    # Vertical line
                    msp.add_line(
                        (x, y - cross_size),
                        (x, y + cross_size),
                        dxfattribs={'layer': 'POSSIBLE_POSITIONS', 'color': 8}
                    )

                    successfully_plotted += 1

                else:
                    if log_callback:
                        log_callback(f"      Possible position {i+1} has insufficient coordinates: {pos}")

            except Exception as pos_error:
                if log_callback:
                    log_callback(f"      Error plotting possible position {i+1}: {pos_error}")

        if log_callback:
            log_callback(f"    Successfully plotted {successfully_plotted}/{len(possible_positions)} possible positions")

    except Exception as e:
        if log_callback:
            log_callback(f"    Error in plot_possible_pile_positions_dxf: {e}")

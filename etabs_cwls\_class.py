"""
Core Data Classes for ETABS CWLS Module

This module contains the core data classes used throughout the ETABS Core Wall
Loading Schedule module. These classes provide structured data management for
file paths, design data, and ETABS version-specific configurations.
"""

from typing import Optional, Dict, Any, List, Callable
from pathlib import Path
import os
from .exceptions import DataValidationError, FileOperationError
from .logging_config import enhanced_log, log_function_entry, log_function_exit, log_validation_result


class file_path:
    """
    File path management class for ETABS CWLS operations.

    This class manages all file paths used in the ETABS Core Wall Loading Schedule
    workflow, including input ETABS files, output Excel files, and various
    configuration files. It provides validation and path management functionality.

    Attributes:
        accessfile1: Path to primary ETABS database file (gravity model)
        accessfile2: Path to secondary ETABS database file (wind model, optional)
        excel_outputfolder: Directory for Excel output files
        design_excel_path: Full path to the design Excel output file
        parameter: Sheet name for parameters
        loadschedule: Sheet name for combined load schedule
        loadschedule_C: Sheet name for column load schedule
        loadschedule_W: Sheet name for wall load schedule
        loadschedule_raw: Sheet name for raw load schedule data
        storylist: Sheet name for story data
        mapping: Sheet name for load mapping
    """

    def __init__(
        self,
        accessfile1: Optional[str] = None,
        accessfile2: Optional[str] = None,
        excel_outputfolder: Optional[str] = None,
        design_excel_path: Optional[str] = None,
        log_callback: Optional[Callable] = None
    ) -> None:
        """
        Initialize file path management.

        Args:
            accessfile1: Path to primary ETABS database file
            accessfile2: Path to secondary ETABS database file (optional)
            excel_outputfolder: Directory for Excel output files
            design_excel_path: Full path to design Excel output file
            log_callback: Optional logging callback function

        Raises:
            DataValidationError: If required paths are invalid
            FileOperationError: If file access validation fails
        """
        log_function_entry(log_callback, "file_path.__init__",
                          accessfile1=accessfile1, accessfile2=accessfile2)

        try:
            # Core file paths
            self.accessfile1 = self._validate_file_path(accessfile1, "accessfile1", required=False)
            self.accessfile2 = self._validate_file_path(accessfile2, "accessfile2", required=False)
            self.excel_outputfolder = excel_outputfolder
            self.design_excel_path = design_excel_path

            # Sheet names with defaults
            self.parameter = 'Parameter'
            self.loadschedule = 'Combined Load Schedule'
            self.loadschedule_C = 'Column Load Schedule'
            self.loadschedule_W = 'Wall Load Schedule'
            self.loadschedule_raw = 'Raw_loadschedule'
            self.storylist = 'Story'
            self.mapping = 'Load Mapping'

            # Legacy attributes for backward compatibility (deprecated)
            self.access_jtreaction = None
            self.mappingG = None
            self.mappingL = None
            self.storydata = None
            self.piersection = None
            self.strip_design = None
            self.Amax_v_value = None
            self.Bmax_v_value = None
            self.parameters = None
            self.punching_shear = None
            self.pile_location = None
            self.strip_thickness_file = None

            log_validation_result(log_callback, "file_path_initialization", True,
                                "File path object initialized successfully")
            log_function_exit(log_callback, "file_path.__init__", "Success")

        except Exception as e:
            log_validation_result(log_callback, "file_path_initialization", False, str(e))
            raise

    def _validate_file_path(self, file_path: Optional[str], field_name: str, required: bool = True) -> Optional[str]:
        """
        Validate a file path.

        Args:
            file_path: Path to validate
            field_name: Name of the field for error reporting
            required: Whether the path is required

        Returns:
            Validated file path or None if not required and not provided

        Raises:
            DataValidationError: If validation fails
            FileOperationError: If file access fails
        """
        if file_path is None:
            if required:
                raise DataValidationError(
                    f"Required file path '{field_name}' cannot be None",
                    field_name=field_name,
                    error_code="MISSING_REQUIRED_PATH"
                )
            return None

        if not isinstance(file_path, str):
            raise DataValidationError(
                f"File path '{field_name}' must be a string, got {type(file_path).__name__}",
                field_name=field_name,
                invalid_value=file_path,
                expected_type="str",
                error_code="INVALID_PATH_TYPE"
            )

        if not file_path.strip():
            raise DataValidationError(
                f"File path '{field_name}' cannot be empty",
                field_name=field_name,
                error_code="EMPTY_PATH"
            )

        # Convert to absolute path
        abs_path = os.path.abspath(file_path)

        # Check if file exists (only for existing files, not output paths)
        if field_name.startswith('access') and not os.path.exists(abs_path):
            raise FileOperationError(
                f"File not found: {abs_path}",
                file_path=abs_path,
                operation="validate",
                error_code="FILE_NOT_FOUND"
            )

        return abs_path

    def validate_all_paths(self, log_callback: Optional[Callable] = None) -> bool:
        """
        Validate all configured file paths.

        Args:
            log_callback: Optional logging callback function

        Returns:
            True if all paths are valid

        Raises:
            DataValidationError: If any path validation fails
        """
        log_function_entry(log_callback, "file_path.validate_all_paths")

        try:
            # Validate primary access file if set
            if self.accessfile1:
                self._validate_file_path(self.accessfile1, "accessfile1", required=True)

            # Validate secondary access file if set
            if self.accessfile2:
                self._validate_file_path(self.accessfile2, "accessfile2", required=False)

            # Validate output folder if set
            if self.excel_outputfolder and not os.path.exists(self.excel_outputfolder):
                try:
                    os.makedirs(self.excel_outputfolder, exist_ok=True)
                except OSError as e:
                    raise FileOperationError(
                        f"Cannot create output directory: {self.excel_outputfolder}",
                        file_path=self.excel_outputfolder,
                        operation="create_directory",
                        error_code="DIRECTORY_CREATE_FAILED"
                    ) from e

            log_validation_result(log_callback, "all_paths_validation", True,
                                "All file paths validated successfully")
            log_function_exit(log_callback, "file_path.validate_all_paths", True)
            return True

        except Exception as e:
            log_validation_result(log_callback, "all_paths_validation", False, str(e))
            raise

    def get_file_info(self) -> Dict[str, Any]:
        """
        Get information about configured files.

        Returns:
            Dictionary containing file information
        """
        info = {
            'accessfile1': {
                'path': self.accessfile1,
                'exists': os.path.exists(self.accessfile1) if self.accessfile1 else False,
                'size': os.path.getsize(self.accessfile1) if self.accessfile1 and os.path.exists(self.accessfile1) else None
            },
            'accessfile2': {
                'path': self.accessfile2,
                'exists': os.path.exists(self.accessfile2) if self.accessfile2 else False,
                'size': os.path.getsize(self.accessfile2) if self.accessfile2 and os.path.exists(self.accessfile2) else None
            },
            'excel_outputfolder': {
                'path': self.excel_outputfolder,
                'exists': os.path.exists(self.excel_outputfolder) if self.excel_outputfolder else False
            },
            'design_excel_path': {
                'path': self.design_excel_path,
                'exists': os.path.exists(self.design_excel_path) if self.design_excel_path else False
            }
        }
        return info
class design_data:
    """
    Design data management class for ETABS CWLS operations.

    This class manages all design data used in the ETABS Core Wall Loading Schedule
    workflow, including DataFrames for various data types, configuration parameters,
    and processing results. It provides structured data management with validation
    and type safety.

    Attributes:
        # Core DataFrames
        df_parameter: DataFrame containing design parameters
        df_schedule: DataFrame containing final load schedule
        df_schedule_raw: DataFrame containing raw load schedule data
        df_schedule_C: DataFrame containing column load schedule
        df_schedule_W: DataFrame containing wall load schedule
        df_mapping: DataFrame containing load case mapping
        df_storydata: DataFrame containing story information
        df_piersection: DataFrame containing pier section properties

        # ETABS Data
        df_jtreaction: DataFrame containing joint reaction data
        df_pierforceG: DataFrame containing gravity pier forces
        df_pierforceL: DataFrame containing lateral pier forces
        df_framepier: DataFrame containing frame pier assignments
        df_shellpier: DataFrame containing shell pier assignments

        # Configuration
        etabsversion: ETABS version (1 for 2016/2017, 2 for 2021)
        use_joint_reactions: Whether to use joint reactions (ETABS 2016/2017 only)
        consider_torsion: Whether to include torsion in calculations

        # Load Factors
        wind_factor: Factor for wind loads
        soil_factor: Factor for soil loads
        earthquake_factor: Factor for earthquake loads
        uplift_factor: Factor for uplift loads
        load_matrix: Matrix for load combination factors

        # Processing Data
        raw_schedules: Dictionary containing raw schedule data by load case
        df_addrotation: Additional rotation angle in radians
    """

    def __init__(
        self,
        etabsversion: Optional[int] = None,
        log_callback: Optional[Callable] = None
    ) -> None:
        """
        Initialize design data management.

        Args:
            etabsversion: ETABS version (1 for 2016/2017, 2 for 2021)
            log_callback: Optional logging callback function

        Raises:
            DataValidationError: If initialization parameters are invalid
        """
        log_function_entry(log_callback, "design_data.__init__", etabsversion=etabsversion)

        try:
            # Core DataFrames
            self.df_parameter = None
            self.df_schedule = None
            self.df_schedule_raw = None
            self.df_schedule_C = None
            self.df_schedule_W = None
            self.df_schedule_final = None
            self.df_mapping = None
            self.df_storydata = None
            self.df_piersection = None

            # ETABS Data
            self.df_jtreaction = None
            self.df_pierforceG = None
            self.df_pierforceL = None
            self.df_framepier = None
            self.df_shellpier = None

            # Configuration
            self.etabsversion = self._validate_etabs_version(etabsversion) if etabsversion else None
            self.use_joint_reactions = False
            self.consider_torsion = True

            # Load Factors
            self.wind_factor = 1.0
            self.soil_factor = 1.0
            self.earthquake_factor = 1.0
            self.uplift_factor = 1.0
            self.load_matrix = None

            # Processing Data
            self.raw_schedules = {}
            self.df_addrotation = 0.0  # Additional rotation in radians

            # Legacy attributes for backward compatibility (deprecated)
            self.df_mappingG = None
            self.df_mappingL = None
            self.basename = None
            self.baselv = None
            self.df_col_design_item = None
            self.df_wall_design_item = None
            self.pierlist = None
            self.load_pattern_frame = None
            self.pier_force_table = None

            log_validation_result(log_callback, "design_data_initialization", True,
                                "Design data object initialized successfully")
            log_function_exit(log_callback, "design_data.__init__", "Success")

        except Exception as e:
            log_validation_result(log_callback, "design_data_initialization", False, str(e))
            raise

    def _validate_etabs_version(self, version: int) -> int:
        """
        Validate ETABS version number.

        Args:
            version: ETABS version to validate

        Returns:
            Validated version number

        Raises:
            DataValidationError: If version is invalid
        """
        if not isinstance(version, int):
            raise DataValidationError(
                f"ETABS version must be an integer, got {type(version).__name__}",
                field_name="etabsversion",
                invalid_value=version,
                expected_type="int",
                error_code="INVALID_VERSION_TYPE"
            )

        if version not in [1, 2]:
            raise DataValidationError(
                f"ETABS version must be 1 (2016/2017) or 2 (2021), got {version}",
                field_name="etabsversion",
                invalid_value=version,
                error_code="INVALID_VERSION_VALUE"
            )

        return version

    def set_load_factors(
        self,
        wind_factor: float = 1.0,
        soil_factor: float = 1.0,
        earthquake_factor: float = 1.0,
        uplift_factor: float = 1.0,
        log_callback: Optional[Callable] = None
    ) -> None:
        """
        Set load factors for calculations.

        Args:
            wind_factor: Factor for wind loads
            soil_factor: Factor for soil loads
            earthquake_factor: Factor for earthquake loads
            uplift_factor: Factor for uplift loads
            log_callback: Optional logging callback function

        Raises:
            DataValidationError: If factors are invalid
        """
        log_function_entry(log_callback, "design_data.set_load_factors",
                          wind_factor=wind_factor, soil_factor=soil_factor,
                          earthquake_factor=earthquake_factor, uplift_factor=uplift_factor)

        try:
            # Validate factors
            factors = {
                'wind_factor': wind_factor,
                'soil_factor': soil_factor,
                'earthquake_factor': earthquake_factor,
                'uplift_factor': uplift_factor
            }

            for name, value in factors.items():
                if not isinstance(value, (int, float)):
                    raise DataValidationError(
                        f"{name} must be a number, got {type(value).__name__}",
                        field_name=name,
                        invalid_value=value,
                        expected_type="float",
                        error_code="INVALID_FACTOR_TYPE"
                    )

                if value < 0:
                    raise DataValidationError(
                        f"{name} must be non-negative, got {value}",
                        field_name=name,
                        invalid_value=value,
                        error_code="NEGATIVE_FACTOR"
                    )

            # Set factors
            self.wind_factor = float(wind_factor)
            self.soil_factor = float(soil_factor)
            self.earthquake_factor = float(earthquake_factor)
            self.uplift_factor = float(uplift_factor)

            log_validation_result(log_callback, "load_factors_setting", True,
                                f"Load factors set: wind={self.wind_factor}, soil={self.soil_factor}, "
                                f"earthquake={self.earthquake_factor}, uplift={self.uplift_factor}")
            log_function_exit(log_callback, "design_data.set_load_factors", "Success")

        except Exception as e:
            log_validation_result(log_callback, "load_factors_setting", False, str(e))
            raise

    def validate_configuration(self, log_callback: Optional[Callable] = None) -> bool:
        """
        Validate the current configuration.

        Args:
            log_callback: Optional logging callback function

        Returns:
            True if configuration is valid

        Raises:
            DataValidationError: If configuration is invalid
        """
        log_function_entry(log_callback, "design_data.validate_configuration")

        try:
            # Validate ETABS version
            if self.etabsversion is not None:
                self._validate_etabs_version(self.etabsversion)

            # Validate load factors
            if any(factor < 0 for factor in [self.wind_factor, self.soil_factor,
                                           self.earthquake_factor, self.uplift_factor]):
                raise DataValidationError(
                    "Load factors must be non-negative",
                    error_code="NEGATIVE_LOAD_FACTORS"
                )

            log_validation_result(log_callback, "configuration_validation", True,
                                "Configuration validated successfully")
            log_function_exit(log_callback, "design_data.validate_configuration", True)
            return True

        except Exception as e:
            log_validation_result(log_callback, "configuration_validation", False, str(e))
            raise

    def get_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current design data state.

        Returns:
            Dictionary containing design data summary
        """
        summary = {
            'configuration': {
                'etabs_version': self.etabsversion,
                'use_joint_reactions': self.use_joint_reactions,
                'consider_torsion': self.consider_torsion
            },
            'load_factors': {
                'wind_factor': self.wind_factor,
                'soil_factor': self.soil_factor,
                'earthquake_factor': self.earthquake_factor,
                'uplift_factor': self.uplift_factor
            },
            'dataframes': {
                'df_parameter': self.df_parameter is not None,
                'df_schedule': self.df_schedule is not None,
                'df_schedule_raw': self.df_schedule_raw is not None,
                'df_mapping': self.df_mapping is not None,
                'df_storydata': self.df_storydata is not None,
                'df_piersection': self.df_piersection is not None,
                'df_jtreaction': self.df_jtreaction is not None,
                'df_pierforceG': self.df_pierforceG is not None,
                'df_pierforceL': self.df_pierforceL is not None,
                'df_framepier': self.df_framepier is not None,
                'df_shellpier': self.df_shellpier is not None
            },
            'processing': {
                'raw_schedules_count': len(self.raw_schedules),
                'additional_rotation': self.df_addrotation
            }
        }
        return summary

class ETABS_converter:
    """
    ETABS version-specific data conversion and mapping class.

    This class handles the differences between ETABS versions by providing
    version-specific table names and column names for database access.
    It abstracts the version differences and provides a consistent interface
    for data access across different ETABS versions.

    The class supports:
    - ETABS 2016/2017 (version 1)
    - ETABS 2021 (version 2)

    Attributes:
        # Table Names (TN prefix)
        TN_PierForce: Table name for pier force data
        TN_StoryData: Table name for story data
        TN_PSProperties: Table name for pier section properties
        TN_FAPier: Table name for frame pier assignments
        TN_SAPier: Table name for shell/area pier assignments

        # Column Names (CN prefix)
        CN_CaseCombo: Column name for load case/combination
        CN_StoryName: Column name for story name
        CN_FPierName: Column name for frame pier name
        CN_SPierName: Column name for shell pier name

        # Version Information
        etabs_version: ETABS version number (1 or 2)
        version_name: Human-readable version name
    """

    def __init__(
        self,
        etabs_version: Optional[int] = None,
        log_callback: Optional[Callable] = None
    ) -> None:
        """
        Initialize ETABS converter with version-specific mappings.

        Args:
            etabs_version: ETABS version (1 for 2016/2017, 2 for 2021)
            log_callback: Optional logging callback function

        Raises:
            DataValidationError: If version is invalid
        """
        log_function_entry(log_callback, "ETABS_converter.__init__", etabs_version=etabs_version)

        try:
            # Initialize with None values
            self.TN_PierForce = None
            self.TN_StoryData = None
            self.TN_PSProperties = None
            self.TN_FAPier = None
            self.TN_SAPier = None
            self.CN_CaseCombo = None
            self.CN_StoryName = None
            self.CN_FPierName = None
            self.CN_SPierName = None

            # Version information
            self.etabs_version = None
            self.version_name = None

            # Set version-specific mappings if version provided
            if etabs_version is not None:
                self.set_version_mappings(etabs_version, log_callback)

            log_validation_result(log_callback, "etabs_converter_initialization", True,
                                f"ETABS converter initialized for version {etabs_version}")
            log_function_exit(log_callback, "ETABS_converter.__init__", "Success")

        except Exception as e:
            log_validation_result(log_callback, "etabs_converter_initialization", False, str(e))
            raise

    def set_version_mappings(
        self,
        etabs_version: int,
        log_callback: Optional[Callable] = None
    ) -> None:
        """
        Set version-specific table and column name mappings.

        Args:
            etabs_version: ETABS version (1 for 2016/2017, 2 for 2021)
            log_callback: Optional logging callback function

        Raises:
            DataValidationError: If version is invalid
        """
        log_function_entry(log_callback, "ETABS_converter.set_version_mappings",
                          etabs_version=etabs_version)

        try:
            # Validate version
            if not isinstance(etabs_version, int):
                raise DataValidationError(
                    f"ETABS version must be an integer, got {type(etabs_version).__name__}",
                    field_name="etabs_version",
                    invalid_value=etabs_version,
                    expected_type="int",
                    error_code="INVALID_VERSION_TYPE"
                )

            if etabs_version == 1:
                # ETABS 2016/2017 mappings
                self.TN_PierForce = "Pier Forces"
                self.TN_StoryData = "Story Data"
                self.TN_PSProperties = "Pier Section Properties"
                self.TN_FAPier = "Frame Assignments - Pier/Spandrel"
                self.TN_SAPier = "Shell Assignments - Pier/Spandrel"
                self.CN_CaseCombo = "CaseCombo"
                self.CN_StoryName = "Name"
                self.CN_FPierName = "Pier"
                self.CN_SPierName = "Pier"
                self.version_name = "ETABS 2016/2017"

            elif etabs_version == 2:
                # ETABS 2021 mappings
                self.TN_PierForce = "Pier Forces"
                self.TN_StoryData = "Story Definitions"
                self.TN_PSProperties = "Pier Section Properties"
                self.TN_FAPier = "Frame Assignments - Pier Labels"
                self.TN_SAPier = "Area Assignments - Pier Labels"
                self.CN_CaseCombo = "Output Case"
                self.CN_StoryName = "Name"
                self.CN_FPierName = "Pier Label"
                self.CN_SPierName = "Pier Name"
                self.version_name = "ETABS 2021"

            else:
                raise DataValidationError(
                    f"Unsupported ETABS version: {etabs_version}. Supported versions are 1 (2016/2017) and 2 (2021)",
                    field_name="etabs_version",
                    invalid_value=etabs_version,
                    error_code="UNSUPPORTED_VERSION"
                )

            self.etabs_version = etabs_version

            log_validation_result(log_callback, "version_mappings_setting", True,
                                f"Version mappings set for {self.version_name}")
            log_function_exit(log_callback, "ETABS_converter.set_version_mappings", "Success")

        except Exception as e:
            log_validation_result(log_callback, "version_mappings_setting", False, str(e))
            raise

    def get_table_mapping(self) -> Dict[str, str]:
        """
        Get current table name mappings.

        Returns:
            Dictionary containing table name mappings
        """
        return {
            'pier_force': self.TN_PierForce,
            'story_data': self.TN_StoryData,
            'pier_section_properties': self.TN_PSProperties,
            'frame_pier_assignments': self.TN_FAPier,
            'shell_pier_assignments': self.TN_SAPier
        }

    def get_column_mapping(self) -> Dict[str, str]:
        """
        Get current column name mappings.

        Returns:
            Dictionary containing column name mappings
        """
        return {
            'case_combo': self.CN_CaseCombo,
            'story_name': self.CN_StoryName,
            'frame_pier_name': self.CN_FPierName,
            'shell_pier_name': self.CN_SPierName
        }

    def validate_mappings(self, log_callback: Optional[Callable] = None) -> bool:
        """
        Validate that all required mappings are set.

        Args:
            log_callback: Optional logging callback function

        Returns:
            True if all mappings are valid

        Raises:
            DataValidationError: If any required mapping is missing
        """
        log_function_entry(log_callback, "ETABS_converter.validate_mappings")

        try:
            required_table_names = [
                'TN_PierForce', 'TN_StoryData', 'TN_PSProperties', 'TN_FAPier', 'TN_SAPier'
            ]
            required_column_names = [
                'CN_CaseCombo', 'CN_StoryName', 'CN_FPierName', 'CN_SPierName'
            ]

            # Check table names
            for attr_name in required_table_names:
                value = getattr(self, attr_name)
                if value is None or not isinstance(value, str) or not value.strip():
                    raise DataValidationError(
                        f"Required table name mapping '{attr_name}' is not set or invalid",
                        field_name=attr_name,
                        invalid_value=value,
                        error_code="MISSING_TABLE_MAPPING"
                    )

            # Check column names
            for attr_name in required_column_names:
                value = getattr(self, attr_name)
                if value is None or not isinstance(value, str) or not value.strip():
                    raise DataValidationError(
                        f"Required column name mapping '{attr_name}' is not set or invalid",
                        field_name=attr_name,
                        invalid_value=value,
                        error_code="MISSING_COLUMN_MAPPING"
                    )

            log_validation_result(log_callback, "mappings_validation", True,
                                f"All mappings validated for {self.version_name}")
            log_function_exit(log_callback, "ETABS_converter.validate_mappings", True)
            return True

        except Exception as e:
            log_validation_result(log_callback, "mappings_validation", False, str(e))
            raise

    def get_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current converter configuration.

        Returns:
            Dictionary containing converter summary
        """
        return {
            'version': {
                'number': self.etabs_version,
                'name': self.version_name
            },
            'table_mappings': self.get_table_mapping(),
            'column_mappings': self.get_column_mapping(),
            'is_configured': all([
                self.TN_PierForce, self.TN_StoryData, self.TN_PSProperties,
                self.TN_FAPier, self.TN_SAPier, self.CN_CaseCombo,
                self.CN_StoryName, self.CN_FPierName, self.CN_SPierName
            ])
        }



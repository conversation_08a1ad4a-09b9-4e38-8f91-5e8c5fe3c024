"""Application configuration and constants."""

import os
from pathlib import Path

# Application info
APP_TITLE = "Foundation Design Automation"
APP_GEOMETRY = "600x600"

# Security settings
MAX_LOGIN_ATTEMPTS = 3
LOGIN_TIMEOUT_MINUTES = 15
SESSION_TOKEN_BYTES = 32
SESSION_VERIFY_INTERVAL_MS = 120000  # 2 minutes
SESSION_DURATION_HOURS = 5

# Email configuration
EMAIL_SENDER = '<EMAIL>'
EMAIL_PASSWORD = 'nunwcsgerkfetpii'  # Consider moving to environment variables for production
EMAIL_DOMAIN = "@asiainfrasolutions.com"

# SSL Configuration - Set these to False if experiencing SSL connection issues
SSL_VERIFY_REQUESTS = False  # Set to True for production, False for development/corporate environments
SSL_STRICT_SMTP = False  # Set to True for production, False if having SSL issues

# URLs
BASE_USER_URL = "https://drive.google.com/file/d/1O3UXw1YogmNHF8D71d1zDuuVqzmQIKCp/view?usp=sharing"
ULTIMATE_USER_URL = "https://drive.google.com/file/d/1Nw_h1LiNB2z2A98SXOeO7XYcYakIBVOB/view?usp=sharing"

# Paths
DATA_DIR = Path.home() / ".FoundationAutomationData"
SECURITY_LOG_PATH = DATA_DIR / "security_log.json"
ICON_PATH = Path("AIS.ico")

# Software version - unified version number
SOFTWARE_VERSION = "V5.3"  # Updated to match notification.py version

# Create data directory if it doesn't exist
DATA_DIR.mkdir(exist_ok=True)

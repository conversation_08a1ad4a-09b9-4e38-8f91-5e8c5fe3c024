"""
Serviceability Limit State (SLS) Angular Rotation Analysis for Foundation Design

This module provides functionality for checking angular rotation limits in foundation slabs
as part of serviceability limit state design verification. Angular rotation analysis is
critical for ensuring structural performance meets comfort and functionality requirements
under service loads, particularly for sensitive equipment and occupancy conditions.

Key Features:
- Angular rotation limit checking against code requirements (1:500 criterion)
- Point-to-slab mapping for distributed analysis across slab elements
- Slab group organization for systematic evaluation and reporting
- Failure identification and detailed reporting for design iteration
- Integration with SAFE analysis results and foundation design workflows

Author: Foundation-Automation Development Team
Compatible with: Foundation design workflows and SAFE 16/22 analysis systems
"""

import numpy as np
import pandas as pd


def check_slab_angular_rotation_xy(file_paths, safe_mdbs, excel_inputs, excel_outputs, design_results, log_callback=None):
    """
    Perform comprehensive angular rotation analysis for foundation slab serviceability verification.
    
    This function analyzes angular rotation results from SAFE finite element analysis to verify
    compliance with serviceability limit state requirements. The analysis maps analysis points
    to slab elements, applies rotation limits, identifies failures, and generates detailed
    reports for design verification and iteration.

    The analysis workflow includes:
    - Point-to-slab mapping from slab geometry definitions
    - Slab group assignment for systematic organization
    - Angular rotation limit checking (1:500 criterion)
    - Failure identification and comprehensive reporting
    - Integration with overall foundation design results
    """   
    # Extract lateral displacement data from SAFE analysis results
    # This contains point-based rotation values (Rz) for serviceability evaluation
    df_lateral_displacement = excel_outputs.LateralDisplacement.copy()
    
    # Extract slab definition data with geometry and grouping information
    # Contains slab marks, groups, and semicolon-delimited point references
    df_slab = excel_inputs.Slab.copy()
    
    # Initialize point-to-slab mapping dictionary for associating analysis points with slab elements
    # This mapping is essential for organizing rotation results by structural element
    slab_point_mapping = {}

    # Process each slab definition to create comprehensive point-to-slab mapping
    for _, row in df_slab.iterrows():
        # Validate that slab has both property definition and point references
        if pd.notna(row['Slab Prop']) and pd.notna(row.get('Points')):
            slab_mark = row['Slab']
            
            # Parse semicolon-delimited point string to extract individual point references
            # Points format: "P1;P2;P3;P4" representing slab corner/edge points
            points = str(row['Points']).split(';')
            
            # Map each point to its parent slab element for rotation analysis organization
            for point in points:
                point = point.strip()  # Remove whitespace that may cause mapping issues
                if point:  # Only process non-empty point references to avoid errors
                    slab_point_mapping[point] = slab_mark    # Apply point-to-slab mapping to lateral displacement data
    # This associates each analysis point with its corresponding slab element
    df_lateral_displacement['Slab_Mark'] = df_lateral_displacement['Point'].map(slab_point_mapping)
    
    # Remove rows where point-to-slab mapping failed (unmapped points)
    # Only analyze points that belong to defined slab elements for meaningful results
    df_lateral_displacement = df_lateral_displacement.dropna(subset=['Slab_Mark'])

    # Create slab-to-group mapping for systematic organization and reporting
    # Groups allow for organized analysis of related slab elements (e.g., by floor, zone)
    slab_group_mapping = df_slab[df_slab['Slab Group'].notna()].set_index('Slab')['Slab Group'].to_dict()
    
    # Apply slab group mapping to organize results by structural groupings
    # This enables systematic review and reporting of rotation performance by zone/group
    df_lateral_displacement['Slab_Group'] = df_lateral_displacement['Slab_Mark'].map(slab_group_mapping)

    # Perform angular rotation serviceability limit checking
    # Calculate absolute rotation values for bi-directional evaluation (positive and negative rotations)
    df_lateral_displacement['abs(Rz) (Rad)'] = df_lateral_displacement['Rz (Rad)'].abs()
    
    # Apply serviceability limit criterion: 1:500 (0.002 radians) maximum rotation
    # This limit ensures acceptable performance for occupant comfort and equipment operation
    df_lateral_displacement['Check (<=1:500)'] = np.where(
        df_lateral_displacement['abs(Rz) (Rad)'] <= 1/500, 'OK', 'NOT OK'
    )

    # Calculate maximum absolute rotation for overall performance assessment
    # This provides a global measure of foundation performance for reporting
    rz_max = df_lateral_displacement['abs(Rz) (Rad)'].max()
    if log_callback:
        log_callback("")
        log_callback("📐 SERVICEABILITY VERIFICATION - Angular Rotation Analysis")
        log_callback("════════════════════════════════════════════════════")
        log_callback("   Checking foundation slab angular rotation compliance with code requirements")
        log_callback(f"   • Maximum angular rotation detected: {rz_max:.6f} radians")
        log_callback(f"   • Maximum angular rotation ratio: 1:{1/rz_max:.0f}")
        log_callback(f"   • Code limit requirement: ≤1:500 (≤{1/500:.6f} radians)")

    # Identify and isolate points that fail the angular rotation criterion
    # Failure points require design attention and potential modification
    df_fail = df_lateral_displacement[df_lateral_displacement['abs(Rz) (Rad)'] > 1/500].copy()

    # Report overall analysis results with appropriate success/failure messaging
    if len(df_fail) > 0:
        if log_callback:
            log_callback("")
            log_callback("❌ FAIL: Angular rotation exceeds allowable code limits")
            log_callback(f"   • Detected ratio 1:{1/rz_max:.0f} > Code limit 1:500")
            log_callback("   • DESIGN ACTION REQUIRED: Foundation stiffness enhancement needed")
            log_callback("   • Consider: Increased slab thickness, additional pile support, or revised layout")
    else:
        if log_callback:
            log_callback("")
            log_callback("✅ PASS: Angular rotation complies with serviceability requirements")
            log_callback(f"   • Detected ratio 1:{1/rz_max:.0f} ≤ Code limit 1:500")
            log_callback("   • Foundation provides adequate rotational stiffness for structural performance")
            log_callback("   • Serviceability criteria satisfied for equipment operation and occupant comfort")

    # Organize results systematically for professional reporting and review
    # Sort by group, slab, output case, and point for logical presentation
    df_lateral_displacement.sort_values(by=['Slab_Group', 'Slab_Mark', 'OutputCase', 'Point'], inplace=True)
    
    # Export comprehensive results to CSV files for design documentation and review
    # Complete results file contains all analysis points with pass/fail status
    df_lateral_displacement.to_csv(file_paths.ResultAngularRotation, index=False)
    
    # Failure summary file contains only points exceeding rotation limits for focused review
    df_fail.to_csv(file_paths.ResultAngularRotationFail, index=False)

    # Update design results container with angular rotation analysis outcomes
    # These results integrate with overall foundation design workflow and documentation
    design_results.AngularRotation = df_lateral_displacement.copy()
    design_results.AngularRotationFail = df_fail.copy()
    
    return design_results

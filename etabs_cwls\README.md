# Foundation Automation - ETABS Wall Loading Schedule Package

The etabs_cwls package provides comprehensive tools for managing and processing wall loading schedules in ETABS (Extended Three-dimensional Analysis of Building Systems), focusing on core wall loading calculations, automation, and professional engineering workflows.

## Version 5.6.9 - Enhanced Professional Edition

This package has been completely redesigned following professional programming standards with enhanced logging, comprehensive error handling, type safety, and zero fallback policy.

## Key Features

- **Enhanced Logging System**: Comprehensive logging with external file storage, multiple log levels, and backward compatibility
- **Zero Fallback Policy**: Strict error handling with no silent failures or default parameters that hide missing inputs
- **Type Safety**: Complete type hints throughout the codebase for better IDE support and error prevention
- **Professional Error Handling**: Custom exception classes with detailed context information
- **External Log Storage**: Log files saved outside application directory for better security and accessibility
- **Comprehensive Documentation**: PEP 257 compliant docstrings with detailed parameter and return type information

## Package Structure

The package is organized into several specialized modules following professional software architecture:

### Core Modules

1. **`__init__.py`**
   - Package initialization with comprehensive module documentation
   - Exports main classes and functions with version information
   - Defines clean package interface

2. **`cwls_gui.py`**
   - Professional graphical user interface for wall loading schedules
   - Enhanced error handling and user feedback
   - Implements:
     - `LoadingScheduleGUI`: Main GUI class with comprehensive validation
     - `main`: Entry point for GUI application

### Enhanced Core Classes

1. **`_class.py`**
   - Professional class definitions with comprehensive validation
   - Implements:
     - `file_path`: Advanced file path management with validation and error handling
     - `design_data`: Robust design data handling with type safety
     - `ETABS_converter`: Version-specific ETABS data conversion with validation

### Enhanced Utility Modules

1. **`logging_config.py`** *(New)*
   - Enhanced logging system with external file storage
   - Multiple log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
   - Function entry/exit logging, performance metrics, validation results
   - Backward compatibility with existing log_callback functions

2. **`exceptions.py`** *(New)*
   - Custom exception classes for specific error scenarios
   - Detailed error context and diagnostic information
   - Professional error hierarchy for better error handling

3. **`_logging.py`** *(Updated)*
   - Legacy logging module with deprecation warnings
   - Backward compatibility bridge to enhanced logging system

4. **`_read.py`** *(Enhanced)*
   - Professional data reading operations with comprehensive error handling
   - Enhanced validation and type checking
   - Performance monitoring and detailed logging
   - Implements:
     - `read_mdbs`: Enhanced Microsoft Access database reading
     - `read_excel`: Professional Excel file reading
     - `validate_database_connection`: Database connectivity validation
     - `get_table_info`: Database structure analysis
     - `get_excel_sheet_names`: Excel file structure analysis

5. **`_write.py`** *(Enhanced)*
   - Professional data writing operations with validation
   - Enhanced Excel formatting and error handling
   - Performance monitoring and detailed logging
   - Implements:
     - `write_excel`: Enhanced Excel writing with validation
     - `format_load_schedule_excel`: Professional load schedule formatting
     - `validate_excel_write_access`: Write permission validation

## Enhanced Key Classes

### LoadingScheduleGUI *(Enhanced)*
- Professional GUI class for wall loading schedules with comprehensive error handling
- Enhanced Features:
  - Interactive data input with real-time validation
  - Professional loading schedule visualization
  - Comprehensive data validation with detailed error messages
  - Enhanced export functionality with progress tracking
  - External log file integration
  - User-friendly error reporting

### file_path *(Completely Redesigned)*
- Advanced file path management class with comprehensive validation
- Enhanced Features:
  - Strict input validation with detailed error messages
  - File existence and permission checking
  - Path normalization and security validation
  - Comprehensive logging of all operations
  - Zero fallback policy with explicit error handling

### design_data *(Completely Redesigned)*
- Robust design data management class with type safety
- Enhanced Features:
  - Comprehensive data validation with type checking
  - Load factor management with validation
  - Configuration validation and summary reporting
  - Enhanced logging of all operations
  - Structured data management with clear interfaces

### ETABS_converter *(Completely Redesigned)*
- Professional ETABS version-specific data conversion class
- Enhanced Features:
  - Version-specific table and column mapping with validation
  - Comprehensive mapping validation
  - Detailed configuration summary reporting
  - Enhanced error handling with context information
  - Support for ETABS 2016/2017 and 2021 with clear version management

## Enhanced Key Functions

### Main Functions
- **`main`**: Professional GUI application entry point
  - Input: None (uses enhanced logging configuration)
  - Output: Professional GUI application with enhanced error handling
  - Features: Advanced GUI initialization, comprehensive event handling, external logging

### Enhanced File Operations
- **`read_mdbs`**: Professional Microsoft Access database reading
  - Input: Database path, table name, optional logging callback
  - Output: Validated pandas DataFrame
  - Features: Comprehensive validation, connection management, detailed error reporting

- **`read_excel`**: Professional Excel file reading
  - Input: Excel path, sheet name, optional logging callback
  - Output: Validated pandas DataFrame
  - Features: File validation, sheet existence checking, comprehensive error handling

- **`write_excel`**: Professional Excel file writing
  - Input: DataFrame, output path, sheet name, optional logging callback
  - Output: Written DataFrame with validation
  - Features: Permission checking, directory creation, format detection, comprehensive logging

- **`format_load_schedule_excel`**: Professional load schedule formatting
  - Input: Excel file, sheet name, load cases, torsion flag, optional logging callback
  - Output: Professionally formatted Excel sheet
  - Features: Advanced formatting, merged headers, borders, comprehensive validation

## Enhanced Usage Examples

### Run Professional GUI Application
```python
from etabs_cwls import main

# Run enhanced wall loading schedule GUI with external logging
main()
```

### Professional Class Usage with Enhanced Logging
```python
from etabs_cwls._class import ETABS_converter, file_path, design_data
from etabs_cwls.logging_config import get_logger

# Set up logging
logger = get_logger()
log_callback = lambda msg, level='INFO': getattr(logger, level.lower())(msg)

# Create ETABS converter with validation
converter = ETABS_converter(etabs_version=2, log_callback=log_callback)

# Create file path manager with validation
file_mgr = file_path(
    accessfile1="model.accdb",
    excel_outputfolder="output",
    log_callback=log_callback
)

# Validate all paths
file_mgr.validate_all_paths(log_callback)

# Create design data manager
design_mgr = design_data(etabs_version=2, log_callback=log_callback)

# Set load factors with validation
design_mgr.set_load_factors(
    wind_factor=1.2,
    soil_factor=1.0,
    earthquake_factor=1.0,
    uplift_factor=0.9,
    log_callback=log_callback
)
```

### Enhanced Data Operations
```python
from etabs_cwls._read import read_mdbs, read_excel
from etabs_cwls._write import write_excel, format_load_schedule_excel
from etabs_cwls.logging_config import get_logger

# Set up logging
logger = get_logger()
log_callback = lambda msg, level='INFO': getattr(logger, level.lower())(msg)

# Read ETABS data with comprehensive validation
pier_forces = read_mdbs("model.accdb", "Pier Forces", log_callback)

# Read Excel parameters with validation
parameters = read_excel("design.xlsx", "Parameters", log_callback)

# Write results with professional formatting
write_excel(pier_forces, "output.xlsx", "Pier Forces", log_callback)

# Apply professional formatting
format_load_schedule_excel(
    "output.xlsx",
    "Load Schedule",
    ["DL", "LL", "W1", "W2"],
    consider_torsion=True,
    log_callback=log_callback
)
```

## Professional Best Practices

1. **Always use enhanced logging**: Utilize the comprehensive logging system for debugging and audit trails
2. **Implement zero fallback policy**: Never use default values that hide missing inputs
3. **Validate all inputs**: Use the comprehensive validation functions before processing
4. **Handle errors explicitly**: Use specific exception types for different error scenarios
5. **Follow type safety**: Utilize type hints and validation throughout your code
6. **Use external log storage**: Logs are automatically saved outside the application directory
7. **Monitor performance**: Use the built-in performance metrics for optimization
8. **Document thoroughly**: Follow the established documentation patterns

## Comprehensive Error Handling

The enhanced package includes professional error handling for:

### File Operations
- **FileOperationError**: File not found, permission denied, file corruption
- Comprehensive file validation and permission checking
- Detailed error context with file paths and operations

### Data Validation
- **DataValidationError**: Invalid data types, missing fields, out-of-range values
- Type checking and range validation
- Detailed field-level error reporting

### ETABS Integration
- **ETABSConnectionError**: Database connection failures, missing tables, version incompatibilities
- Database connectivity validation
- Version-specific error handling

### Calculations
- **CalculationError**: Mathematical errors, convergence failures, invalid parameters
- Comprehensive calculation validation
- Detailed parameter error reporting

### Configuration
- **ConfigurationError**: Missing configuration, invalid settings, environment issues
- Configuration validation and reporting
- Environment setup verification

### GUI Operations
- **GUIError**: Widget failures, event handling errors, display issues
- User-friendly error messages
- Graceful error recovery

## Enhanced Integration Points

This professional package integrates seamlessly with:
- **ETABS Software**: Full compatibility with ETABS 2016/2017 and 2021
- **Foundation Automation System**: Enhanced integration with comprehensive logging
- **Enhanced Logging System**: Integration with fdn_agent.pile_estimation.utils.logging_utils
- **Professional GUI Framework**: tkinter with enhanced error handling and user feedback
- **External Log Storage**: Automatic log file management outside application directory

## Enhanced Dependencies

The package relies on carefully managed dependencies:
- **tkinter**: Enhanced GUI framework with professional error handling
- **pandas**: Advanced data handling with comprehensive validation
- **openpyxl**: Professional Excel operations with formatting
- **pyodbc**: Microsoft Access database connectivity with error handling
- **pathlib**: Modern path handling for cross-platform compatibility
- **typing**: Complete type hint support for better IDE integration

## Version Information

- **Current Version**: 5.6.9 (Enhanced Professional Edition)
- **Foundation Automation Compatibility**: V5.6.9+
- **Python Compatibility**: 3.8+
- **ETABS Compatibility**: 2016/2017 and 2021

## Enhanced Logging Configuration

### External Log File Storage
Log files are automatically saved to external locations for better security and accessibility:

1. **Primary Location**: `~/Documents/Foundation_Automation_Logs/etabs_cwls/`
2. **Fallback Location**: System temp directory
3. **Log File Format**: `etabs_cwls_YYYYMMDD_HHMMSS.log`

### Log Levels and Display
- **GUI Applications**: All log levels displayed
- **Terminal Applications**: Only ERROR/WARNING/CRITICAL displayed
- **File Logging**: All levels with detailed formatting
- **Performance Metrics**: Automatic timing and efficiency measurements

## Related Documentation

For comprehensive documentation on related packages:
- [safe_api/README.md](../safe_api/README.md): SAFE integration with enhanced logging
- [design_fdn/README.md](../design_fdn/README.md): Foundation design with professional workflows
- [config/README.md](../config/README.md): Configuration management with validation
- [fdn_agent/pile_estimation/utils/logging_utils.py](../fdn_agent/pile_estimation/utils/logging_utils.py): Enhanced logging system reference

## Professional Implementation Notes

1. **Zero Fallback Policy**: All operations implement strict error handling with no silent failures
2. **Comprehensive Validation**: Data validation performed at all stages with detailed error reporting
3. **Enhanced Logging**: Professional logging system with external file storage and performance metrics
4. **Type Safety**: Complete type hints throughout the codebase for better IDE support
5. **ETABS Compatibility**: Maintained and enhanced compatibility with version-specific handling
6. **Professional UI**: Enhanced user interface components with comprehensive error handling
7. **External Log Storage**: Log files saved outside application directory for better security
8. **Performance Monitoring**: Built-in performance metrics and timing for optimization
9. **Backward Compatibility**: Maintained compatibility with existing log_callback functions
10. **Professional Documentation**: PEP 257 compliant docstrings with comprehensive parameter documentation

## Migration from Previous Versions

### For Existing Code
- Legacy logging functions are automatically redirected to the enhanced system
- Existing log_callback functions continue to work with backward compatibility
- All existing class interfaces are maintained with enhanced validation

### Recommended Updates
- Update to use the new enhanced logging system for better debugging
- Implement the new exception classes for better error handling
- Utilize the new validation functions for improved data integrity
- Take advantage of external log file storage for better log management

## Support and Maintenance

This enhanced version provides:
- **Professional Error Messages**: Clear, actionable error messages with context
- **Comprehensive Logging**: Detailed audit trails for debugging and compliance
- **Performance Monitoring**: Built-in metrics for optimization and troubleshooting
- **Type Safety**: Complete type checking for better development experience
- **External Log Storage**: Secure log file management outside application directory

"""
Enhanced Clustering Algorithms with Machine Learning

This module provides advanced clustering algorithms for structural element grouping:
- Adaptive clustering with automatic parameter tuning
- Multi-criteria clustering (spatial, load-based, structural)
- Machine learning-enhanced cluster validation
- Dynamic cluster optimization and refinement
- Performance-optimized implementations

Author: Foundation Automation System
Date: June 18, 2025
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Callable, Tuple
from dataclasses import dataclass
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from scipy.spatial.distance import pdist, squareform
from scipy.cluster.hierarchy import linkage, fcluster
import warnings

# Enhanced logging system imports
try:
    from ..utils.logging_utils import (
        enhanced_log,
        log_function_entry,
        log_function_exit,
        log_validation_result,
        log_calculation_result,
        log_performance_metric,
        log_error_with_context,
        log_algorithm_step,
        create_timed_logger
    )
    ENHANCED_LOGGING_AVAILABLE = True
except ImportError:
    ENHANCED_LOGGING_AVAILABLE = False
    # Fallback logging functions
    def enhanced_log(log_callback, message, level='INFO'): 
        if log_callback: log_callback(f"[{level}] {message}")
    def log_function_entry(log_callback, func_name, **kwargs): 
        if log_callback: log_callback(f"[DEBUG] Entering {func_name}")
    def log_function_exit(log_callback, func_name, **kwargs): 
        if log_callback: log_callback(f"[DEBUG] Exiting {func_name}")
    def log_validation_result(log_callback, name, passed, details): 
        if log_callback: log_callback(f"[{'INFO' if passed else 'WARNING'}] {name}: {'PASSED' if passed else 'FAILED'} - {details}")
    def log_calculation_result(log_callback, name, result, unit): 
        if log_callback: log_callback(f"[INFO] {name}: {result} {unit}")
    def log_performance_metric(log_callback, name, value, unit): 
        if log_callback: log_callback(f"[DEBUG] {name}: {value} {unit}")
    def log_error_with_context(log_callback, error, context): 
        if log_callback: log_callback(f"[ERROR] {context}: {error}")
    def log_algorithm_step(log_callback, algorithm, step, details=""): 
        if log_callback: log_callback(f"[DEBUG] {algorithm} - {step}: {details}")
    class create_timed_logger:
        def __init__(self, log_callback, operation_name):
            self.log_callback = log_callback
            self.operation_name = operation_name
            self.start_time = None
        def __enter__(self):
            self.start_time = time.time()
            return self
        def __exit__(self, exc_type, exc_val, exc_tb):
            if self.start_time and self.log_callback:
                duration = time.time() - self.start_time
                self.log_callback(f"[DEBUG] {self.operation_name} completed in {duration:.3f}s")

@dataclass
class EnhancedClusteringConfig:
    """Configuration for enhanced clustering algorithms."""
    
    # Algorithm Selection
    primary_algorithm: str = "adaptive_hierarchical"  # "kmeans", "dbscan", "hierarchical", "adaptive_hierarchical"
    
    # Adaptive Parameters
    auto_tune_parameters: bool = True
    parameter_search_iterations: int = 10
    
    # Multi-criteria Clustering
    use_spatial_features: bool = True
    use_load_features: bool = True
    use_structural_features: bool = True
    feature_weights: Dict[str, float] = None
    
    # Clustering Validation
    use_multiple_metrics: bool = True
    silhouette_weight: float = 0.4
    calinski_harabasz_weight: float = 0.3
    davies_bouldin_weight: float = 0.3
    
    # Performance Optimization
    use_dimensionality_reduction: bool = True
    pca_variance_threshold: float = 0.95
    max_features: int = 10
    
    # Constraint Handling
    min_cluster_size: int = 1
    max_cluster_size: int = 50
    min_clusters: int = 1
    max_clusters: int = 20
    
    # Distance Thresholds
    spatial_distance_threshold: float = 10.0
    load_similarity_threshold: float = 0.8
    
    def __post_init__(self):
        """Initialize default feature weights if not provided."""
        if self.feature_weights is None:
            self.feature_weights = {
                'spatial': 0.5,
                'load': 0.3,
                'structural': 0.2
            }

class EnhancedClusteringEngine:
    """
    Enhanced clustering engine with machine learning capabilities.
    
    Features:
    - Adaptive algorithm selection based on data characteristics
    - Multi-criteria clustering with spatial, load, and structural features
    - Automatic parameter tuning using validation metrics
    - Performance optimization with dimensionality reduction
    - Comprehensive cluster validation and quality assessment
    """
    
    def __init__(self, config: EnhancedClusteringConfig, log_callback: Optional[Callable] = None):
        """Initialize the enhanced clustering engine."""
        log_function_entry(log_callback, "EnhancedClusteringEngine.__init__")
        
        self.config = config
        self.log_callback = log_callback
        
        # Initialize components
        self.scaler = StandardScaler()
        self.pca = PCA() if config.use_dimensionality_reduction else None
        
        # Performance tracking
        self.clustering_history = []
        self.validation_scores = []
        
        log_validation_result(log_callback, "clustering_engine_initialization", True,
                            f"Algorithm: {config.primary_algorithm}")
        
        log_function_exit(log_callback, "EnhancedClusteringEngine.__init__")
    
    def extract_features(self, elements: List[Dict[str, Any]]) -> np.ndarray:
        """
        Extract multi-criteria features from structural elements.
        
        Args:
            elements: List of structural elements with coordinates and properties
            
        Returns:
            Feature matrix for clustering
        """
        log_function_entry(self.log_callback, "extract_features", element_count=len(elements))
        
        with create_timed_logger(self.log_callback, "feature_extraction") as timer:
            try:
                features = []
                
                for element in elements:
                    element_features = []
                    
                    # Spatial features
                    if self.config.use_spatial_features:
                        x = element.get('x', 0.0)
                        y = element.get('y', 0.0)
                        z = element.get('z', 0.0)
                        element_features.extend([x, y, z])
                    
                    # Load features
                    if self.config.use_load_features:
                        fx = element.get('fx', 0.0)
                        fy = element.get('fy', 0.0)
                        fz = element.get('fz', 0.0)
                        mx = element.get('mx', 0.0)
                        my = element.get('my', 0.0)
                        mz = element.get('mz', 0.0)
                        
                        # Calculate load magnitudes
                        force_magnitude = np.sqrt(fx**2 + fy**2 + fz**2)
                        moment_magnitude = np.sqrt(mx**2 + my**2 + mz**2)
                        
                        element_features.extend([force_magnitude, moment_magnitude])
                    
                    # Structural features
                    if self.config.use_structural_features:
                        element_type = element.get('type', 'unknown')
                        # Convert element type to numerical feature
                        type_encoding = {'column': 1.0, 'wall': 2.0, 'beam': 3.0, 'unknown': 0.0}
                        type_value = type_encoding.get(element_type.lower(), 0.0)
                        
                        size = element.get('size', 1.0)
                        element_features.extend([type_value, size])
                    
                    features.append(element_features)
                
                feature_matrix = np.array(features)
                
                log_calculation_result(self.log_callback, "feature_extraction",
                                     f"Shape: {feature_matrix.shape}", "features")
                
                log_function_exit(self.log_callback, "extract_features",
                                result=f"Extracted {feature_matrix.shape[1]} features")
                
                return feature_matrix
                
            except Exception as e:
                log_error_with_context(self.log_callback, e, "extract_features")
                raise
    
    def preprocess_features(self, features: np.ndarray) -> np.ndarray:
        """
        Preprocess features with scaling and dimensionality reduction.
        
        Args:
            features: Raw feature matrix
            
        Returns:
            Preprocessed feature matrix
        """
        log_function_entry(self.log_callback, "preprocess_features", 
                          feature_shape=features.shape)
        
        try:
            # Scale features
            scaled_features = self.scaler.fit_transform(features)
            log_algorithm_step(self.log_callback, "Preprocessing", "Feature scaling completed")
            
            # Apply dimensionality reduction if enabled
            if self.config.use_dimensionality_reduction and features.shape[1] > self.config.max_features:
                self.pca.fit(scaled_features)
                
                # Find number of components for desired variance
                cumsum_variance = np.cumsum(self.pca.explained_variance_ratio_)
                n_components = np.argmax(cumsum_variance >= self.config.pca_variance_threshold) + 1
                n_components = min(n_components, self.config.max_features)
                
                # Apply PCA with selected components
                self.pca = PCA(n_components=n_components)
                processed_features = self.pca.fit_transform(scaled_features)
                
                log_algorithm_step(self.log_callback, "Preprocessing", 
                                 f"PCA applied: {features.shape[1]} -> {n_components} features",
                                 f"Variance retained: {cumsum_variance[n_components-1]:.3f}")
            else:
                processed_features = scaled_features
            
            log_calculation_result(self.log_callback, "feature_preprocessing",
                                 f"Final shape: {processed_features.shape}", "")
            
            log_function_exit(self.log_callback, "preprocess_features")
            
            return processed_features
            
        except Exception as e:
            log_error_with_context(self.log_callback, e, "preprocess_features")
            raise
    
    def adaptive_clustering(self, features: np.ndarray) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        Perform adaptive clustering with automatic parameter tuning.
        
        Args:
            features: Preprocessed feature matrix
            
        Returns:
            Tuple of (cluster_labels, clustering_metrics)
        """
        log_function_entry(self.log_callback, "adaptive_clustering",
                          feature_shape=features.shape)
        
        with create_timed_logger(self.log_callback, "adaptive_clustering") as timer:
            try:
                best_labels = None
                best_score = -np.inf
                best_metrics = {}
                
                # Try different numbers of clusters
                cluster_range = range(self.config.min_clusters, 
                                    min(self.config.max_clusters + 1, len(features)))
                
                for n_clusters in cluster_range:
                    try:
                        # Apply clustering algorithm
                        if self.config.primary_algorithm == "kmeans":
                            clusterer = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                            labels = clusterer.fit_predict(features)
                        
                        elif self.config.primary_algorithm == "hierarchical":
                            clusterer = AgglomerativeClustering(n_clusters=n_clusters)
                            labels = clusterer.fit_predict(features)
                        
                        elif self.config.primary_algorithm == "adaptive_hierarchical":
                            # Use distance-based hierarchical clustering
                            distance_matrix = pdist(features)
                            linkage_matrix = linkage(distance_matrix, method='ward')
                            labels = fcluster(linkage_matrix, n_clusters, criterion='maxclust') - 1
                        
                        else:
                            raise ValueError(f"Unsupported algorithm: {self.config.primary_algorithm}")
                        
                        # Validate cluster quality
                        if len(np.unique(labels)) > 1:
                            metrics = self._calculate_clustering_metrics(features, labels)
                            combined_score = self._calculate_combined_score(metrics)
                            
                            if combined_score > best_score:
                                best_score = combined_score
                                best_labels = labels.copy()
                                best_metrics = metrics.copy()
                                best_metrics['n_clusters'] = n_clusters
                        
                        log_algorithm_step(self.log_callback, "Adaptive Clustering",
                                         f"Tested {n_clusters} clusters",
                                         f"Score: {combined_score:.3f}" if len(np.unique(labels)) > 1 else "Invalid")
                    
                    except Exception as e:
                        enhanced_log(self.log_callback, f"Failed clustering with {n_clusters} clusters: {e}", 'WARNING')
                        continue
                
                if best_labels is None:
                    # Fallback to single cluster
                    best_labels = np.zeros(len(features), dtype=int)
                    best_metrics = {'n_clusters': 1, 'silhouette_score': 0.0}
                    enhanced_log(self.log_callback, "Using fallback single cluster", 'WARNING')
                
                log_calculation_result(self.log_callback, "adaptive_clustering_result",
                                     f"{best_metrics.get('n_clusters', 1)} clusters", "")
                
                log_function_exit(self.log_callback, "adaptive_clustering",
                                result=f"Best score: {best_score:.3f}")
                
                return best_labels, best_metrics
                
            except Exception as e:
                log_error_with_context(self.log_callback, e, "adaptive_clustering")
                raise
    
    def _calculate_clustering_metrics(self, features: np.ndarray, labels: np.ndarray) -> Dict[str, float]:
        """Calculate comprehensive clustering validation metrics."""
        try:
            metrics = {}
            
            # Silhouette score
            if len(np.unique(labels)) > 1:
                metrics['silhouette_score'] = silhouette_score(features, labels)
                metrics['calinski_harabasz_score'] = calinski_harabasz_score(features, labels)
                metrics['davies_bouldin_score'] = davies_bouldin_score(features, labels)
            else:
                metrics['silhouette_score'] = 0.0
                metrics['calinski_harabasz_score'] = 0.0
                metrics['davies_bouldin_score'] = float('inf')
            
            # Cluster size distribution
            unique_labels, counts = np.unique(labels, return_counts=True)
            metrics['cluster_size_std'] = np.std(counts)
            metrics['min_cluster_size'] = np.min(counts)
            metrics['max_cluster_size'] = np.max(counts)
            
            return metrics
            
        except Exception as e:
            log_error_with_context(self.log_callback, e, "_calculate_clustering_metrics")
            return {'silhouette_score': 0.0}
    
    def _calculate_combined_score(self, metrics: Dict[str, float]) -> float:
        """Calculate combined clustering quality score."""
        try:
            silhouette = metrics.get('silhouette_score', 0.0)
            calinski = metrics.get('calinski_harabasz_score', 0.0)
            davies_bouldin = metrics.get('davies_bouldin_score', float('inf'))
            
            # Normalize scores
            silhouette_norm = max(0, silhouette)  # Already in [-1, 1]
            calinski_norm = min(1.0, calinski / 1000.0)  # Normalize by typical range
            davies_bouldin_norm = max(0, 1.0 - davies_bouldin / 10.0)  # Invert and normalize
            
            # Calculate weighted combination
            combined_score = (
                self.config.silhouette_weight * silhouette_norm +
                self.config.calinski_harabasz_weight * calinski_norm +
                self.config.davies_bouldin_weight * davies_bouldin_norm
            )
            
            return combined_score
            
        except Exception as e:
            log_error_with_context(self.log_callback, e, "_calculate_combined_score")
            return 0.0
    
    def cluster_elements(self, elements: List[Dict[str, Any]]) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        Main clustering function that processes structural elements.
        
        Args:
            elements: List of structural elements to cluster
            
        Returns:
            Tuple of (cluster_labels, clustering_results)
        """
        log_function_entry(self.log_callback, "cluster_elements", element_count=len(elements))
        
        with create_timed_logger(self.log_callback, "enhanced_clustering") as timer:
            try:
                # Extract features
                features = self.extract_features(elements)
                
                # Preprocess features
                processed_features = self.preprocess_features(features)
                
                # Perform adaptive clustering
                labels, metrics = self.adaptive_clustering(processed_features)
                
                # Prepare results
                results = {
                    'cluster_labels': labels,
                    'n_clusters': len(np.unique(labels)),
                    'clustering_metrics': metrics,
                    'feature_shape': features.shape,
                    'processed_feature_shape': processed_features.shape,
                    'algorithm_used': self.config.primary_algorithm
                }
                
                log_calculation_result(self.log_callback, "clustering_completed",
                                     f"{results['n_clusters']} clusters", "")
                
                log_function_exit(self.log_callback, "cluster_elements",
                                result=f"Clustered {len(elements)} elements into {results['n_clusters']} groups")
                
                return labels, results
                
            except Exception as e:
                log_error_with_context(self.log_callback, e, "cluster_elements")
                raise

def create_enhanced_clustering_engine(config: Optional[EnhancedClusteringConfig] = None,
                                     log_callback: Optional[Callable] = None) -> EnhancedClusteringEngine:
    """
    Factory function to create an enhanced clustering engine.
    
    Args:
        config: Optional configuration (uses default if None)
        log_callback: Optional logging callback
        
    Returns:
        Configured enhanced clustering engine
    """
    log_function_entry(log_callback, "create_enhanced_clustering_engine")
    
    if config is None:
        config = EnhancedClusteringConfig()
        enhanced_log(log_callback, "Using default enhanced clustering configuration", 'INFO')
    
    engine = EnhancedClusteringEngine(config, log_callback)
    
    log_function_exit(log_callback, "create_enhanced_clustering_engine",
                    result="Enhanced clustering engine created")
    
    return engine

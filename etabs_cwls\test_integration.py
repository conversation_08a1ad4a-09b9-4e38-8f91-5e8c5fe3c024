"""
Integration Testing Script for ETABS CWLS Refactored Modules

This script performs comprehensive integration testing to validate:
1. Import validation and circular dependency detection
2. API compatibility testing
3. Cross-module integration
4. Backward compatibility verification
5. Error handling consistency
6. Logging integration

Version: 5.6.9
Author: Foundation Automation Team
"""

import sys
import traceback
import importlib
from typing import List, Dict, Any, Optional
import warnings

# Suppress warnings for cleaner test output
warnings.filterwarnings('ignore')

class IntegrationTester:
    """Comprehensive integration tester for refactored ETABS CWLS modules."""
    
    def __init__(self):
        self.test_results = {}
        self.modules_to_test = [
            'console_interface',
            'configuration_manager', 
            'file_path_manager',
            'initialization_processor',
            'pier_force_processor',
            'joint_reaction_processor',
            'coordinate_transformer',
            'schedule_generator'
        ]
        
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all integration tests and return results."""
        print("=" * 80)
        print("ETABS CWLS Integration Testing - Refactored Modules")
        print("=" * 80)
        
        # Test 1: Import Validation
        print("\n1. Testing Import Validation...")
        self.test_results['import_validation'] = self.test_import_validation()
        
        # Test 2: Circular Dependency Check
        print("\n2. Testing Circular Dependencies...")
        self.test_results['circular_dependencies'] = self.test_circular_dependencies()
        
        # Test 3: API Compatibility
        print("\n3. Testing API Compatibility...")
        self.test_results['api_compatibility'] = self.test_api_compatibility()
        
        # Test 4: Cross-Module Integration
        print("\n4. Testing Cross-Module Integration...")
        self.test_results['cross_module_integration'] = self.test_cross_module_integration()
        
        # Test 5: Backward Compatibility
        print("\n5. Testing Backward Compatibility...")
        self.test_results['backward_compatibility'] = self.test_backward_compatibility()
        
        # Test 6: Error Handling Consistency
        print("\n6. Testing Error Handling...")
        self.test_results['error_handling'] = self.test_error_handling()
        
        # Test 7: Logging Integration
        print("\n7. Testing Logging Integration...")
        self.test_results['logging_integration'] = self.test_logging_integration()
        
        # Generate summary
        self.print_test_summary()
        
        return self.test_results
    
    def test_import_validation(self) -> Dict[str, Any]:
        """Test that all modules can be imported without errors."""
        results = {'passed': 0, 'failed': 0, 'errors': []}
        
        for module_name in self.modules_to_test:
            try:
                module = importlib.import_module(f'etabs_cwls.{module_name}')
                print(f"  ✓ {module_name}: Import successful")
                results['passed'] += 1
                
                # Check if module has expected attributes
                if hasattr(module, '__all__'):
                    exported_functions = getattr(module, '__all__')
                    print(f"    - Exports {len(exported_functions)} functions: {', '.join(exported_functions[:3])}{'...' if len(exported_functions) > 3 else ''}")
                
            except Exception as e:
                print(f"  ❌ {module_name}: Import failed - {str(e)}")
                results['failed'] += 1
                results['errors'].append(f"{module_name}: {str(e)}")
        
        return results
    
    def test_circular_dependencies(self) -> Dict[str, Any]:
        """Test for circular dependencies between modules."""
        results = {'passed': 0, 'failed': 0, 'errors': [], 'dependency_map': {}}
        
        try:
            # Import all modules and check their dependencies
            for module_name in self.modules_to_test:
                try:
                    module = importlib.import_module(f'etabs_cwls.{module_name}')
                    
                    # Get module file to analyze imports
                    module_file = module.__file__
                    if module_file:
                        with open(module_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # Extract imports from this package
                        imports = []
                        for line in content.split('\n'):
                            line = line.strip()
                            if line.startswith('from .') and 'import' in line:
                                import_module = line.split('from .')[1].split(' import')[0].strip()
                                if import_module in self.modules_to_test:
                                    imports.append(import_module)
                        
                        results['dependency_map'][module_name] = imports
                        print(f"  ✓ {module_name}: Dependencies - {imports if imports else 'None'}")
                        results['passed'] += 1
                        
                except Exception as e:
                    print(f"  ❌ {module_name}: Dependency analysis failed - {str(e)}")
                    results['failed'] += 1
                    results['errors'].append(f"{module_name}: {str(e)}")
            
            # Check for circular dependencies
            circular_deps = self._detect_circular_dependencies(results['dependency_map'])
            if circular_deps:
                results['errors'].extend([f"Circular dependency detected: {dep}" for dep in circular_deps])
                print(f"  ❌ Circular dependencies found: {circular_deps}")
            else:
                print("  ✓ No circular dependencies detected")
                
        except Exception as e:
            results['errors'].append(f"Circular dependency test failed: {str(e)}")
            
        return results
    
    def _detect_circular_dependencies(self, dependency_map: Dict[str, List[str]]) -> List[str]:
        """Detect circular dependencies in the dependency map."""
        circular_deps = []
        
        def has_circular_dependency(module: str, target: str, visited: set, path: List[str]) -> bool:
            if module == target and len(path) > 1:
                circular_deps.append(" -> ".join(path + [module]))
                return True
            
            if module in visited:
                return False
                
            visited.add(module)
            
            for dep in dependency_map.get(module, []):
                if has_circular_dependency(dep, target, visited.copy(), path + [module]):
                    return True
            
            return False
        
        for module in dependency_map:
            for dep in dependency_map.get(module, []):
                has_circular_dependency(dep, module, set(), [module])
        
        return circular_deps
    
    def test_api_compatibility(self) -> Dict[str, Any]:
        """Test that the refactored _main.py properly delegates to new modules."""
        results = {'passed': 0, 'failed': 0, 'errors': []}
        
        try:
            # Test main module import
            import etabs_cwls._main as main_module
            print("  ✓ _main module import successful")
            results['passed'] += 1
            
            # Test that main functions are available
            expected_functions = [
                'main', 'assign_ETABS_converter', 'filepath_selection',
                'initialization', 'create_schedule_pier_force', 
                'create_schedule_joint_reaction', 'create_raw_schedule',
                'create_final_schedule', 'integer_round'
            ]
            
            for func_name in expected_functions:
                if hasattr(main_module, func_name):
                    print(f"  ✓ Function '{func_name}' available in _main")
                    results['passed'] += 1
                else:
                    print(f"  ❌ Function '{func_name}' missing from _main")
                    results['failed'] += 1
                    results['errors'].append(f"Missing function: {func_name}")
            
            # Test __all__ export
            if hasattr(main_module, '__all__'):
                exported = getattr(main_module, '__all__')
                print(f"  ✓ _main exports {len(exported)} functions")
                results['passed'] += 1
            else:
                print("  ❌ _main missing __all__ export")
                results['failed'] += 1
                results['errors'].append("Missing __all__ in _main")
                
        except Exception as e:
            print(f"  ❌ API compatibility test failed: {str(e)}")
            results['failed'] += 1
            results['errors'].append(f"API compatibility: {str(e)}")
        
        return results
    
    def test_cross_module_integration(self) -> Dict[str, Any]:
        """Test data flow and integration between modules."""
        results = {'passed': 0, 'failed': 0, 'errors': []}
        
        try:
            # Test that modules can import each other's functions
            from etabs_cwls.configuration_manager import assign_ETABS_converter
            from etabs_cwls.file_path_manager import filepath_selection
            from etabs_cwls.coordinate_transformer import integer_round
            print("  ✓ Cross-module function imports successful")
            results['passed'] += 1
            
            # Test that core classes are accessible
            from etabs_cwls import _class
            file_path = _class.file_path()
            design_data = _class.design_data()
            ETABS_converter = _class.ETABS_converter()
            print("  ✓ Core class instantiation successful")
            results['passed'] += 1
            
            # Test integer_round function (simple integration test)
            test_value = integer_round(1.7)
            if test_value == 2:
                print("  ✓ integer_round function works correctly")
                results['passed'] += 1
            else:
                print(f"  ❌ integer_round function failed: expected 2, got {test_value}")
                results['failed'] += 1
                results['errors'].append(f"integer_round failed: {test_value}")
            
        except Exception as e:
            print(f"  ❌ Cross-module integration test failed: {str(e)}")
            results['failed'] += 1
            results['errors'].append(f"Cross-module integration: {str(e)}")
        
        return results
    
    def test_backward_compatibility(self) -> Dict[str, Any]:
        """Test that existing code using old imports still works."""
        results = {'passed': 0, 'failed': 0, 'errors': []}
        
        try:
            # Test old-style imports from _main
            from etabs_cwls._main import main, integer_round
            print("  ✓ Old-style _main imports work")
            results['passed'] += 1
            
            # Test package-level imports
            from etabs_cwls import main as package_main
            from etabs_cwls import integer_round as package_integer_round
            print("  ✓ Package-level imports work")
            results['passed'] += 1
            
            # Test that functions are the same
            if main == package_main:
                print("  ✓ main function consistency verified")
                results['passed'] += 1
            else:
                print("  ❌ main function inconsistency detected")
                results['failed'] += 1
                results['errors'].append("main function inconsistency")
            
            if integer_round == package_integer_round:
                print("  ✓ integer_round function consistency verified")
                results['passed'] += 1
            else:
                print("  ❌ integer_round function inconsistency detected")
                results['failed'] += 1
                results['errors'].append("integer_round function inconsistency")
                
        except Exception as e:
            print(f"  ❌ Backward compatibility test failed: {str(e)}")
            results['failed'] += 1
            results['errors'].append(f"Backward compatibility: {str(e)}")
        
        return results
    
    def test_error_handling(self) -> Dict[str, Any]:
        """Test that error handling works consistently across modules."""
        results = {'passed': 0, 'failed': 0, 'errors': []}
        
        try:
            # Test custom exceptions are available
            from etabs_cwls.exceptions import (
                DataValidationError, FileOperationError, ETABSConnectionError,
                CalculationError, ConfigurationError
            )
            print("  ✓ Custom exceptions import successfully")
            results['passed'] += 1
            
            # Test that exceptions can be raised and caught
            try:
                raise DataValidationError("Test error", field_name="test", invalid_value="test")
            except DataValidationError as e:
                if hasattr(e, 'field_name') and e.field_name == "test":
                    print("  ✓ DataValidationError works correctly")
                    results['passed'] += 1
                else:
                    print("  ❌ DataValidationError missing attributes")
                    results['failed'] += 1
                    results['errors'].append("DataValidationError missing attributes")
            
        except Exception as e:
            print(f"  ❌ Error handling test failed: {str(e)}")
            results['failed'] += 1
            results['errors'].append(f"Error handling: {str(e)}")
        
        return results
    
    def test_logging_integration(self) -> Dict[str, Any]:
        """Test that logging works consistently across modules."""
        results = {'passed': 0, 'failed': 0, 'errors': []}
        
        try:
            # Test logging functions import
            from etabs_cwls.logging_config import (
                enhanced_log, log_function_entry, log_function_exit,
                get_logger
            )
            print("  ✓ Logging functions import successfully")
            results['passed'] += 1
            
            # Test logger creation
            logger = get_logger()
            if logger:
                print("  ✓ Logger creation successful")
                results['passed'] += 1
            else:
                print("  ❌ Logger creation failed")
                results['failed'] += 1
                results['errors'].append("Logger creation failed")
            
            # Test basic logging (should not raise exceptions)
            enhanced_log(None, "Test log message", 'INFO')
            log_function_entry(None, "test_function")
            log_function_exit(None, "test_function", "test_result")
            print("  ✓ Basic logging functions work")
            results['passed'] += 1
            
        except Exception as e:
            print(f"  ❌ Logging integration test failed: {str(e)}")
            results['failed'] += 1
            results['errors'].append(f"Logging integration: {str(e)}")
        
        return results
    
    def print_test_summary(self):
        """Print a comprehensive test summary."""
        print("\n" + "=" * 80)
        print("INTEGRATION TEST SUMMARY")
        print("=" * 80)
        
        total_passed = sum(result.get('passed', 0) for result in self.test_results.values())
        total_failed = sum(result.get('failed', 0) for result in self.test_results.values())
        total_tests = total_passed + total_failed
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {total_passed} ✓")
        print(f"Failed: {total_failed} {'❌' if total_failed > 0 else '✓'}")
        print(f"Success Rate: {(total_passed/total_tests*100):.1f}%" if total_tests > 0 else "No tests run")
        
        # Print detailed results
        for test_name, result in self.test_results.items():
            print(f"\n{test_name.replace('_', ' ').title()}:")
            print(f"  Passed: {result.get('passed', 0)}")
            print(f"  Failed: {result.get('failed', 0)}")
            if result.get('errors'):
                print("  Errors:")
                for error in result['errors'][:3]:  # Show first 3 errors
                    print(f"    - {error}")
                if len(result['errors']) > 3:
                    print(f"    ... and {len(result['errors']) - 3} more")
        
        print("\n" + "=" * 80)
        if total_failed == 0:
            print("🎉 ALL INTEGRATION TESTS PASSED! 🎉")
            print("The refactored modules are working correctly together.")
        else:
            print("⚠️  SOME TESTS FAILED")
            print("Please review the errors above and fix the issues.")
        print("=" * 80)


def main():
    """Run the integration tests."""
    tester = IntegrationTester()
    results = tester.run_all_tests()
    
    # Return exit code based on results
    total_failed = sum(result.get('failed', 0) for result in results.values())
    return 0 if total_failed == 0 else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

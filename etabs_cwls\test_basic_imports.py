"""
Basic Import Test for ETABS CWLS Refactored Modules

This script tests basic imports to identify any immediate import issues.
"""

def test_basic_imports():
    """Test basic imports of all refactored modules."""
    print("Testing basic imports...")
    
    try:
        # Test core module imports
        print("1. Testing core modules...")
        from . import _class
        from . import exceptions
        from . import logging_config
        print("   ✓ Core modules imported successfully")
        
        # Test refactored module imports
        print("2. Testing refactored modules...")
        from . import console_interface
        from . import configuration_manager
        from . import file_path_manager
        from . import initialization_processor
        from . import pier_force_processor
        from . import joint_reaction_processor
        from . import coordinate_transformer
        from . import schedule_generator
        print("   ✓ All refactored modules imported successfully")
        
        # Test main functions
        print("3. Testing main function imports...")
        from .configuration_manager import assign_ETABS_converter
        from .file_path_manager import filepath_selection
        from .initialization_processor import initialization
        from .pier_force_processor import create_schedule_pier_force
        from .joint_reaction_processor import create_schedule_joint_reaction
        from .coordinate_transformer import create_raw_schedule, integer_round
        from .schedule_generator import create_final_schedule
        print("   ✓ All main functions imported successfully")
        
        # Test backward compatibility
        print("4. Testing backward compatibility...")
        from ._main import main, assign_ETABS_converter as main_assign
        print("   ✓ Backward compatibility imports successful")
        
        # Test package-level imports
        print("5. Testing package-level imports...")
        import etabs_cwls
        if hasattr(etabs_cwls, 'main'):
            print("   ✓ Package-level main function available")
        if hasattr(etabs_cwls, 'integer_round'):
            print("   ✓ Package-level integer_round function available")
        
        print("\n🎉 ALL BASIC IMPORT TESTS PASSED! 🎉")
        return True
        
    except ImportError as e:
        print(f"\n❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected Error: {e}")
        return False

def test_function_availability():
    """Test that key functions are available and callable."""
    print("\nTesting function availability...")
    
    try:
        from .coordinate_transformer import integer_round
        
        # Test integer_round function
        result = integer_round(1.7)
        if result == 2:
            print("   ✓ integer_round function works correctly")
        else:
            print(f"   ❌ integer_round function failed: expected 2, got {result}")
            return False
        
        # Test that classes can be instantiated
        from ._class import file_path, design_data, ETABS_converter
        
        fp = file_path()
        dd = design_data()
        ec = ETABS_converter()
        
        print("   ✓ Core classes can be instantiated")
        
        print("\n🎉 ALL FUNCTION AVAILABILITY TESTS PASSED! 🎉")
        return True
        
    except Exception as e:
        print(f"\n❌ Function availability test failed: {e}")
        return False

def test_cross_module_integration():
    """Test basic cross-module integration."""
    print("\nTesting cross-module integration...")
    
    try:
        # Test that modules can import from each other
        from .pier_force_processor import create_schedule_pier_force
        from .coordinate_transformer import create_raw_schedule
        from .schedule_generator import create_final_schedule
        
        print("   ✓ Cross-module function imports successful")
        
        # Test that exceptions are properly available
        from .exceptions import DataValidationError, FileOperationError
        
        # Test creating and catching an exception
        try:
            raise DataValidationError("Test error", field_name="test", invalid_value="test")
        except DataValidationError as e:
            if hasattr(e, 'field_name'):
                print("   ✓ Custom exceptions work correctly")
            else:
                print("   ❌ Custom exceptions missing attributes")
                return False
        
        print("\n🎉 ALL CROSS-MODULE INTEGRATION TESTS PASSED! 🎉")
        return True
        
    except Exception as e:
        print(f"\n❌ Cross-module integration test failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("ETABS CWLS Basic Import Testing")
    print("=" * 60)
    
    success = True
    success &= test_basic_imports()
    success &= test_function_availability()
    success &= test_cross_module_integration()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED - INTEGRATION SUCCESSFUL! 🎉")
    else:
        print("❌ SOME TESTS FAILED - PLEASE REVIEW ERRORS ABOVE")
    print("=" * 60)

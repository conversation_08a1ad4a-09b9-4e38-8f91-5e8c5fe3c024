"""Application controller for Foundation Design Automation."""
import tkinter as tk
from tkinter import messagebox
import logging
import secrets
import hashlib
import threading
import time
from datetime import datetime
from typing import Optional
import os  # Import os for environment variable access

# UI imports
from ui.login_frame import LoginFrame
from ui.version_selection_frame import VersionSelectionFrame
from ui.main_menu_frame import MainMenuFrame
from ui.components import create_menu_bar, setup_window_icon, show_about

# Configuration and email imports
from config.app_config import (
    APP_TITLE, SESSION_VERIFY_INTERVAL_MS, BASE_USER_URL, ULTIMATE_USER_URL,
    SESSION_DURATION_HOURS, SOFTWARE_VERSION
)
from email_notifications.notification import (
    generate_password_key, 
    send_password_email, 
    send_email_log
)

# Security manager import
from auth.security_manager import SecurityManager

# Import the application modules
from build_fem.builder_gui import SafeModelBuilderGUI
from design_fdn.designer_gui import SafeDesign<PERSON><PERSON>cker<PERSON><PERSON>
from etabs_cwls.cwls_gui import LoadingScheduleGUI
from fdn_agent.agent_gui import FdnAgentGUI


class ApplicationController:
    """Main application controller with integrated security management."""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        self.root.title(APP_TITLE)
        self.root.geometry("600x600")
        self.root.resizable(False, False)
        
        # Setup window
        setup_window_icon(self.root)
        create_menu_bar(self.root, show_about)
        
        # Initialize security manager
        self.security_manager = SecurityManager()
        
        # Initialize variables
        self.user_name = ""
        self.user_email = ""
        self.login_status = False
        self.password_salt = ""
        self.password_hash = ""
        self.session_token = ""
        self.start_time = time.time()
        self.duration = SESSION_DURATION_HOURS * 60 * 60  # Convert hours to seconds
        self.user_type = "Base"  # Default to Base version
        self.dual_access = False  # Track if user has access to both versions
        
        # Timer and session management
        self.timer_active = False
        self.time_label = None
        self.verifying_session = False
        
        # Log the session configuration
        logging.info(f"Session initialized with {SESSION_DURATION_HOURS} hour timeout ({self.duration} seconds)")
        
        # Check if any user is locked out at startup
        if self.security_manager.is_locked_out():
            messagebox.showerror(
                "Account Locked",
                "This account is locked due to too many failed login attempts.\n"
                "Please try again after 15 minutes have passed."
            )
            self.root.after(1000, self.perform_exit)
            return
        
        # Start the timer thread
        self.monitor_thread = threading.Thread(target=self.monitor_elapsed_time, daemon=True)
        self.monitor_thread.start()
        
        # Initialize login screen
        self.show_login()

    def show_login(self):
        """Display login frame."""
        # Clear any existing widgets
        for widget in self.root.winfo_children():
            widget.destroy()
        
        LoginFrame(
            self.root,
            on_send_password=self.handle_send_password,
            on_login=self.handle_login,
            on_dev_login=self.handle_dev_login if __debug__ else None,
            is_locked=False
        )

    def handle_send_password(self, username: str):
        """Handle password generation and sending."""
        if not username:
            messagebox.showwarning("Input Error", "Please enter a username.")
            return
        
        # Clean username
        username = username.strip().lower()
        if '@' in username:
            username = username.split('@')[0]
        
        self.user_name = username
        logging.info(f"Attempting to authorize user: {username}")
        
        # TEMPORARY: Add bypass for testing
        if os.getenv('BYPASS_AUTH_CHECK', '').lower() == 'true':
            logging.warning("BYPASSING AUTHORIZATION CHECK - TEST MODE ONLY")
            self.user_type = "Ultimate"
            self.dual_access = False
            
            # Generate password and continue as normal
            password_key = generate_password_key()
            self.password_salt = secrets.token_hex(16)
            self.password_hash = self.security_manager.hash_password(password_key, self.password_salt)
            
            send_password_email(username, password_key)
            messagebox.showinfo(
                "Password Sent (TEST MODE)", 
                f"Password has been sent to {self.security_manager.get_user_email(username)}\n\n"
                "NOTE: Running in TEST MODE - Authorization bypassed"
            )
            return
          # Check if user is locked out
        if self.security_manager.is_locked_out(username):
            messagebox.showerror(
                "Account Locked",
                "This account is temporarily locked due to too many failed attempts. "
                "Please try again in 15 minutes."
            )
            self.root.after(1000, self.perform_exit)
            return
        
        try:
            # Check user authorization and access level
            logging.info(f"Checking authorization for user: {username}")
            
            # Fetch authorized users from both Base and Ultimate lists
            base_users = self.security_manager.get_authorized_users(BASE_USER_URL)
            ultimate_users = self.security_manager.get_authorized_users(ULTIMATE_USER_URL)
            
            # Log what we found
            logging.info(f"Base users found: {len(base_users)}")
            logging.info(f"Ultimate users found: {len(ultimate_users)}")
            
            # Check user access level
            is_base_user = username in base_users
            is_ultimate_user = username in ultimate_users
            
            if not is_base_user and not is_ultimate_user:
                logging.warning(f"User {username} not found in any authorized user list")
                messagebox.showerror(
                    "Access Denied",
                    f"User '{username}' is not authorized to use this application.\n"
                    "Please contact your administrator for access."
                )
                return
            
            # Determine user type and dual access
            if is_ultimate_user:
                self.user_type = "Ultimate"
                self.dual_access = is_base_user  # True if user has access to both
            else:
                self.user_type = "Base"
                self.dual_access = False
            
            logging.info(f"User {username} authorized - Type: {self.user_type}, Dual access: {self.dual_access}")
            
            # Generate password and continue as normal
            password_key = generate_password_key()
            self.password_salt = secrets.token_hex(16)
            self.password_hash = self.security_manager.hash_password(password_key, self.password_salt)
            
            # Send password email
            send_password_email(username, password_key)
            
            # Show success message
            messagebox.showinfo(
                "Password Sent",
                f"Password has been sent to {self.security_manager.get_user_email(username)}\n"
            )
            
        except Exception as e:
            logging.error(f"Error in handle_send_password: {e}")
            messagebox.showerror(
                "Error",
                f"Failed to send password: {str(e)}\n"
                "Please try again or contact support if the problem persists."
            )

    def handle_login(self, password: str):
        """Handle user login."""
        if not password:
            messagebox.showwarning("Input Error", "Please enter a password.")
            return
        
        if not self.password_hash:
            messagebox.showerror("Error", "Please request a password first")
            return
        
        # Check if user is locked out
        if self.security_manager.is_locked_out(self.user_name):
            messagebox.showerror(
                "Account Locked",
                "This account is temporarily locked due to too many failed attempts. "
                "Please try again in 15 minutes."
            )
            self.root.after(1000, self.perform_exit)
            return
        
        # Hash input password with stored salt
        input_password_hash = self.security_manager.hash_password(password, self.password_salt)
        
        if input_password_hash == self.password_hash:
            self.login_status = True
            # Generate a session token
            self.session_token = self.security_manager.generate_session_token()
            # Reset the start time when logging in
            self.start_time = time.time()
            logging.info(f"Login successful for {self.user_name}. Timer reset. Duration set to {self.duration/3600:.2f} hours")
            # Log successful login
            self.security_manager.log_successful_login(self.user_name, self.user_type, self.session_token)
            
            # Securely clear password data from memory
            self.password_hash = ""
            self.password_salt = ""
            
            # If user has access to both versions, ask them to choose
            if self.dual_access:
                self.ask_version_choice()
            else:
                messagebox.showinfo("Success", f"Welcome, {self.user_name} - {self.user_type} Version")
                self.show_main_application()
        else:
            # Log failed attempt and check if account should be locked
            should_lock = self.security_manager.log_failed_attempt(self.user_name)
            
            if should_lock:
                messagebox.showerror(
                    "Account Locked",
                    "Too many failed login attempts. Your account has been locked for 15 minutes."
                )
                self.root.after(1000, self.perform_exit)
            else:
                messagebox.showerror("Error", "Wrong Password! Please try again.")

    def handle_dev_login(self):
        """Development login bypass."""
        self.user_name = "dev_user"
        self.user_type = "Ultimate"  # Default, but will be overridden by selection
        self.dual_access = True  # Enable version selection for developers
        self.login_status = True
        self.session_token = self.security_manager.generate_session_token()
        self.start_time = time.time()
        logging.info("Development login used")
        
        # Show version selection dialog
        self.ask_version_choice()
    
    def ask_version_choice(self):
        """Ask user which version they want to use if they have access to both."""
        VersionSelectionFrame(self.root, self.user_name, self.on_version_selected)
    
    def on_version_selected(self, version: str):
        """Handle version selection callback."""
        self.user_type = version
        messagebox.showinfo("Success", f"Welcome, {self.user_name} - {version} Version")
        self.show_main_application()

    def show_main_application(self):
        """Show main application after successful login."""
        # Start timer
        self.timer_active = True
        
        # Start session verification
        self.root.after(SESSION_VERIFY_INTERVAL_MS, self.verify_session)
        
        # Define menu items based on user type
        if self.user_type == "Ultimate":
            menu_items = {
                "ETABS Connection and CWLS": self.run_etabs_cwls,
                "SAFE Model Builder": self.run_safe_model_builder,
                "SAFE Design Checker": self.run_safe_design_checker,
                "AI Agent": self.run_foundation_agent
            }
        else:  # Base version
            menu_items = {
                "SAFE Model Builder": self.run_safe_model_builder
            }
        
        # Show main menu
        main_menu = MainMenuFrame(
            self.root, 
            self.user_name, 
            self.user_type, 
            menu_items, 
            self.handle_logout
        )
        
        # Get timer label and start updates
        self.time_label = main_menu.get_timer_label()
        self.update_timer()

    def handle_logout(self):
        """Handle user logout."""
        if messagebox.askyesno("Logout", "Are you sure you want to log out?"):
            # Clear sensitive data
            self.login_status = False
            self.session_token = ""
            self.password_hash = ""
            self.password_salt = ""
            
            # Deactivate timer updates
            self.timer_active = False
            
            # Log logout
            if self.user_name:
                send_email_log(self.user_name, "Logout", self.user_type)
            
            # Reset user data
            self.user_type = "Base"
            self.dual_access = False
            
            # Return to login screen
            self.show_login()

    def verify_session(self):
        """Verify the session is still valid."""
        # Skip verification if not logged in or if verification is already in progress
        if not self.login_status or not self.session_token or self.verifying_session:
            if self.root.winfo_exists():
                self.root.after(SESSION_VERIFY_INTERVAL_MS, self.verify_session)
            return
        
        self.verifying_session = True
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        # Log session verification attempt
        logging.debug(f"Session verification attempt. Elapsed: {elapsed_time/3600:.2f} hours")
        
        try:
            # Check if session token is valid
            session_valid = self.security_manager.verify_session(self.user_name, self.session_token)
            logging.debug(f"Session verification result: {session_valid}")
            
            if not session_valid:
                logging.warning(f"Session verification failed for user {self.user_name}")
                if elapsed_time >= self.duration:
                    logging.info(f"Actual session timeout reached via verification: {elapsed_time/3600:.2f} hours")
                    self.close_program()
                    return
                else:
                    logging.warning(f"Session verification failed but timer not expired. Elapsed: {elapsed_time/3600:.2f} hours. Continuing session.")
        except Exception as e:
            logging.error(f"Error verifying session: {e}")
        finally:
            self.verifying_session = False
            
            # Schedule next verification if everything is okay and window still exists
            if self.root.winfo_exists() and self.login_status:
                self.root.after(SESSION_VERIFY_INTERVAL_MS, self.verify_session)

    def update_timer(self):
        """Update the timer display."""
        if not self.timer_active or not self.time_label:
            return
        
        try:
            current_time = time.time()
            elapsed_time = current_time - self.start_time
            remaining_time = max(0, self.duration - elapsed_time)
            
            hours = int(remaining_time // 3600)
            minutes = int((remaining_time % 3600) // 60)
            seconds = int(remaining_time % 60)
            
            time_str = f"Time remaining: {hours:02d}:{minutes:02d}:{seconds:02d}"
            self.time_label.update_time(time_str)
            
            if remaining_time > 0:
                self.root.after(1000, self.update_timer)
            else:
                logging.info("Timer UI reached zero, but close_program should have been called by monitor thread")
        except Exception as e:
            logging.error(f"Error updating timer: {e}")

    def monitor_elapsed_time(self):
        """Monitor elapsed time in background thread."""
        logging.info(f"Timer monitor thread started. Will timeout after {self.duration/3600:.2f} hours ({self.duration} seconds)")

        while True:
            try:
                if not (self.root and self.root.winfo_exists()):
                    logging.info("Monitor thread: Root window no longer exists. Exiting thread.")
                    break

                if self.login_status and self.timer_active:
                    current_time = time.time()
                    elapsed_since_login = current_time - self.start_time
                    
                    if elapsed_since_login >= self.duration:
                        logging.info(f"Session duration ({self.duration}s) exceeded. Elapsed: {elapsed_since_login:.2f}s. Initiating logout.")
                        self.root.after(0, self.close_program)
                        logging.info("Monitor thread: close_program scheduled. Exiting thread.")
                        break
                
            except tk.TclError as e:
                logging.warning(f"Monitor thread: TclError ('{e}'). Likely root window destroyed. Exiting thread.")
                break
            except Exception as e:
                logging.error(f"Error in monitor_elapsed_time: {e}. Exiting thread.")
                break
            
            time.sleep(1)

        logging.info("monitor_elapsed_time thread has finished.")

    def close_program(self):
        """Close program due to timeout."""
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        logging.info(f"close_program() called. Elapsed time: {elapsed_time/3600:.2f} hours, Expected duration: {self.duration/3600:.2f} hours")
        logging.info("Session timeout reached. Closing the application.")
        
        # Deactivate timer to prevent further updates
        self.timer_active = False
        
        messagebox.showinfo("Time Expired", f"Your {self.duration/3600:.1f}-hour session has expired.")
        # Schedule the actual exit after a short delay to ensure the messagebox is closed
        self.root.after(200, self.perform_exit)

    def perform_exit(self):
        """Perform application exit."""
        try:
            logging.info("Exiting application.")
            if self.root:
                self.root.destroy()
        except Exception as e:
            logging.error(f"Error during application exit: {e}")

    # Business logic methods
    def run_etabs_cwls(self):
        """Launch ETABS CWLs module."""
        try:
            # Create a new window for the LoadingScheduleGUI
            cwls_window = tk.Toplevel(self.root)
            cwls_window.transient(self.root)
            cwls_window.grab_set()
            
            # Initialize the LoadingScheduleGUI
            cwls_app = LoadingScheduleGUI(cwls_window)
            
            # Wait for the window to close
            self.root.wait_window(cwls_window)
            
        except Exception as e:
            logging.error(f"Error running ETABS CWLs: {e}")
            messagebox.showerror("Error", f"Failed to launch ETABS CWLs: {str(e)}")
    
    def run_safe_model_builder(self):
        """Launch SAFE Model Builder."""
        try:
            # Create the SafeModelBuilderGUI with proper parameters
            SafeModelBuilderGUI(
                parent=self.root,
                user_type=self.user_type,
                user_email=self.security_manager.get_user_email(self.user_name),
                username=self.user_name
            )
        except Exception as e:
            logging.error(f"Error running SAFE Model Builder: {e}")
            messagebox.showerror("Error", f"Failed to launch SAFE Model Builder: {str(e)}")
    
    def run_safe_design_checker(self):
        """Launch SAFE Design Checker."""
        try:
            # Create the SafeDesignCheckerGUI with the main window as parent
            SafeDesignCheckerGUI(root=self.root)
        except Exception as e:
            logging.error(f"Error running SAFE Design Checker: {e}")
            messagebox.showerror("Error", f"Failed to launch SAFE Design Checker: {str(e)}")
    
    def run_foundation_agent(self):
        """Launch Foundation Agent."""
        try:
            # Create the FdnAgentGUI with proper parameters
            FdnAgentGUI(
                parent=self.root,
                user_type=self.user_type,
                user_email=self.security_manager.get_user_email(self.user_name),
                username=self.user_name
            )
        except Exception as e:
            logging.error(f"Error running Foundation Agent: {e}")
            messagebox.showerror("Error", f"Failed to launch Foundation Agent: {str(e)}")

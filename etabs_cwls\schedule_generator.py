"""
Schedule Generator Module for ETABS CWLS

This module handles the generation of final load schedules by applying load
factors and combinations to raw schedule data. It provides professional
load combination processing with enhanced error handling and validation.

The module implements professional programming standards with:
- Enhanced logging system with external file storage
- Zero fallback policy with explicit error handling
- Comprehensive type safety and validation
- Professional load combination algorithms

Key Functions:
    create_final_schedule: Create final schedule with applied load factors
    apply_load_factor: Apply single load factor to schedule components
    apply_load_matrix: Apply load combination matrix
    merge_schedules: Merge multiple schedules into combined output

Version: 5.6.9
Author: Foundation Automation Team
Copyright: © 2023-2025 Foundation Automation. All rights reserved.
"""

import pandas as pd
from typing import Optional, Callable, Dict, List

from . import _class
from .coordinate_transformer import integer_round
from .exceptions import CalculationError, DataValidationError
from .logging_config import (
    enhanced_log, log_function_entry, log_function_exit,
    log_validation_result, log_calculation_result, log_error_with_context
)


def create_final_schedule(
    loadcase: str, 
    design_data: _class.design_data, 
    raw_schedules: Dict[str, pd.DataFrame], 
    consider_torsion: bool = True,
    log_callback: Optional[Callable] = None
) -> pd.DataFrame:
    """
    Create final schedule by applying load factors to raw schedule data.
    
    This function applies appropriate load factors based on the load case type
    (wind, soil, earthquake, uplift) or uses load combination matrices for
    standard load types (DL, SDL, LL). It implements engineering rounding
    for final values.
    
    Args:
        loadcase: Load case identifier
        design_data: Design data object containing load factors
        raw_schedules: Dictionary of raw schedules by load case
        consider_torsion: Whether to include torsion in processing
        log_callback: Optional logging callback function
        
    Returns:
        DataFrame containing final schedule with applied factors
        
    Raises:
        CalculationError: If load factor application fails
        DataValidationError: If input data is invalid
        
    Example:
        >>> final_schedule = create_final_schedule("W1", design_data, raw_schedules)
    """
    log_function_entry(log_callback, "create_final_schedule", 
                      loadcase=loadcase, consider_torsion=consider_torsion)
    
    try:
        # Input validation
        if loadcase not in raw_schedules:
            raise DataValidationError(
                f"Load case '{loadcase}' not found in raw schedules",
                field_name="loadcase",
                invalid_value=loadcase,
                error_code="LOADCASE_NOT_FOUND"
            )
        
        # Initialize with copy of raw schedule
        df_final_schedule = raw_schedules[loadcase].copy()
        
        # Get load factors from design data
        wind_factor = getattr(design_data, 'wind_factor', 1.0)
        soil_factor = getattr(design_data, 'soil_factor', 1.0)
        earthquake_factor = getattr(design_data, 'earthquake_factor', 1.0)
        uplift_factor = getattr(design_data, 'uplift_factor', 1.0)
        load_matrix = getattr(design_data, 'load_matrix', None)
        
        enhanced_log(log_callback, f"Applying load factors for {loadcase}", 'DEBUG')
        log_calculation_result(log_callback, f"{loadcase}_wind_factor", wind_factor, "factor")
        
        # Define force/moment components to process
        cols = ['Vx', 'Vy', 'P', 'Mx', 'My']
        if consider_torsion:
            cols.append('Mz')
        
        # Apply factors based on load case type
        if loadcase.upper().startswith('W'):  # Wind loads
            enhanced_log(log_callback, f"Applying wind factor {wind_factor} to {loadcase}", 'DEBUG')
            apply_load_factor(df_final_schedule, loadcase, cols, wind_factor, log_callback)
            
        elif loadcase == 'SL':  # Soil loads
            enhanced_log(log_callback, f"Applying soil factor {soil_factor} to {loadcase}", 'DEBUG')
            apply_load_factor(df_final_schedule, loadcase, cols, soil_factor, log_callback)
            
        elif loadcase == 'Eqr':  # Earthquake loads
            enhanced_log(log_callback, f"Applying earthquake factor {earthquake_factor} to {loadcase}", 'DEBUG')
            apply_load_factor(df_final_schedule, loadcase, cols, earthquake_factor, log_callback)
            
        elif loadcase == 'Uplift':  # Uplift loads
            enhanced_log(log_callback, f"Applying uplift factor {uplift_factor} to {loadcase}", 'DEBUG')
            apply_load_factor(df_final_schedule, loadcase, cols, uplift_factor, log_callback)
            
        elif loadcase in ['DL', 'SDL', 'LL'] and load_matrix is not None:
            enhanced_log(log_callback, f"Applying load matrix to {loadcase}", 'DEBUG')
            apply_load_matrix(df_final_schedule, loadcase, cols, load_matrix, raw_schedules, log_callback)
            
        else:
            enhanced_log(log_callback, f"No load factor applied to {loadcase} (using raw values)", 'DEBUG')
        
        # Sort by pier name for consistent ordering
        df_final_schedule = df_final_schedule.sort_values(by='Pier').reset_index(drop=True)
        
        log_calculation_result(log_callback, f"{loadcase}_final_piers", len(df_final_schedule), "piers")
        log_function_exit(log_callback, "create_final_schedule", f"{len(df_final_schedule)} piers")
        
        return df_final_schedule
        
    except Exception as e:
        log_error_with_context(log_callback, e, f"create_final_schedule({loadcase})")
        raise


def apply_load_factor(
    df_schedule: pd.DataFrame, 
    loadcase: str, 
    cols: List[str], 
    factor: float,
    log_callback: Optional[Callable] = None
) -> None:
    """
    Apply a single load factor to all force/moment components.
    
    Args:
        df_schedule: Schedule DataFrame to modify
        loadcase: Load case identifier
        cols: List of force/moment component names
        factor: Load factor to apply
        log_callback: Optional logging callback function
        
    Raises:
        CalculationError: If factor application fails
    """
    log_function_entry(log_callback, "apply_load_factor", 
                      loadcase=loadcase, factor=factor, components=len(cols))
    
    try:
        for col in cols:
            col_name = f'{loadcase}{col}'
            if col_name in df_schedule.columns:
                original_values = df_schedule[col_name].copy()
                df_schedule[col_name] = (df_schedule[col_name] * factor).apply(integer_round)
                
                # Log sample transformation for verification
                if len(original_values) > 0:
                    sample_original = original_values.iloc[0]
                    sample_final = df_schedule[col_name].iloc[0]
                    enhanced_log(log_callback, 
                               f"{col_name}: {sample_original:.2f} * {factor} = {sample_final}", 'DEBUG')
        
        log_function_exit(log_callback, "apply_load_factor", "Success")
        
    except Exception as e:
        raise CalculationError(
            f"Failed to apply load factor {factor} to {loadcase}: {str(e)}",
            calculation_type="load_factor_application",
            input_parameters={"loadcase": loadcase, "factor": factor},
            error_code="LOAD_FACTOR_APPLICATION_FAILED"
        ) from e


def apply_load_matrix(
    df_schedule: pd.DataFrame, 
    loadcase: str, 
    cols: List[str], 
    load_matrix: List[List[float]], 
    raw_schedules: Dict[str, pd.DataFrame],
    log_callback: Optional[Callable] = None
) -> None:
    """
    Apply load combination matrix to create combined load effects.
    
    Args:
        df_schedule: Schedule DataFrame to modify
        loadcase: Load case identifier
        cols: List of force/moment component names
        load_matrix: Load combination matrix
        raw_schedules: Dictionary of raw schedules
        log_callback: Optional logging callback function
        
    Raises:
        CalculationError: If matrix application fails
    """
    log_function_entry(log_callback, "apply_load_matrix", loadcase=loadcase)
    
    try:
        load_types = ['DL', 'SDL', 'LL']
        col_idx = load_types.index(loadcase)
        
        enhanced_log(log_callback, f"Applying load matrix for {loadcase} (index {col_idx})", 'DEBUG')
        
        for col in cols:
            col_name = f'{loadcase}{col}'
            if col_name in df_schedule.columns:
                for idx in df_schedule.index:
                    final_val = 0
                    
                    # Combine effects from all load types using matrix factors
                    for i, lt in enumerate(load_types):
                        if lt in raw_schedules:
                            lt_col_name = f'{lt}{col}'
                            if lt_col_name in raw_schedules[lt].columns:
                                val = raw_schedules[lt].at[idx, lt_col_name]
                                factor = load_matrix[i][col_idx]
                                final_val += factor * val
                    
                    df_schedule.at[idx, col_name] = integer_round(final_val)
        
        log_function_exit(log_callback, "apply_load_matrix", "Success")
        
    except Exception as e:
        raise CalculationError(
            f"Failed to apply load matrix to {loadcase}: {str(e)}",
            calculation_type="load_matrix_application",
            input_parameters={"loadcase": loadcase},
            error_code="LOAD_MATRIX_APPLICATION_FAILED"
        ) from e


def merge_schedules(
    schedules: Dict[str, pd.DataFrame],
    log_callback: Optional[Callable] = None
) -> pd.DataFrame:
    """
    Merge multiple schedules into a single combined DataFrame.
    
    Args:
        schedules: Dictionary of schedules to merge
        log_callback: Optional logging callback function
        
    Returns:
        Combined DataFrame with all schedules merged
        
    Raises:
        CalculationError: If merge operation fails
    """
    log_function_entry(log_callback, "merge_schedules", schedule_count=len(schedules))
    
    try:
        if not schedules:
            enhanced_log(log_callback, "Warning: No schedules to merge", 'WARNING')
            return pd.DataFrame()
        
        # Start with the first schedule
        schedule_names = list(schedules.keys())
        merged_df = schedules[schedule_names[0]].copy()
        
        # Merge remaining schedules
        for schedule_name in schedule_names[1:]:
            try:
                merged_df = pd.merge(
                    merged_df, schedules[schedule_name], 
                    on='Pier', how='outer', validate='1:1'
                )
                enhanced_log(log_callback, f"Merged schedule: {schedule_name}", 'DEBUG')
                
            except Exception as e:
                raise CalculationError(
                    f"Failed to merge schedule '{schedule_name}': {str(e)}",
                    calculation_type="schedule_merge",
                    input_parameters={"schedule_name": schedule_name},
                    error_code="SCHEDULE_MERGE_FAILED"
                ) from e
        
        # Reset index and sort by pier name
        merged_df = merged_df.sort_values(by='Pier').reset_index(drop=True)
        
        log_calculation_result(log_callback, "merged_schedules", len(schedules), "schedules")
        log_calculation_result(log_callback, "merged_rows", len(merged_df), "rows")
        log_calculation_result(log_callback, "merged_columns", len(merged_df.columns), "columns")
        
        enhanced_log(log_callback, f"Successfully merged {len(schedules)} schedules", 'INFO')
        log_function_exit(log_callback, "merge_schedules", f"{len(merged_df)} rows")
        
        return merged_df
        
    except Exception as e:
        log_error_with_context(log_callback, e, "merge_schedules")
        raise


def validate_schedule_data(
    schedule: pd.DataFrame,
    schedule_name: str,
    log_callback: Optional[Callable] = None
) -> bool:
    """
    Validate schedule data for consistency and completeness.
    
    Args:
        schedule: Schedule DataFrame to validate
        schedule_name: Name of the schedule for error reporting
        log_callback: Optional logging callback function
        
    Returns:
        True if schedule is valid
        
    Raises:
        DataValidationError: If validation fails
    """
    log_function_entry(log_callback, "validate_schedule_data", schedule_name=schedule_name)
    
    try:
        # Check if DataFrame is valid
        if not isinstance(schedule, pd.DataFrame):
            raise DataValidationError(
                f"Schedule '{schedule_name}' must be a DataFrame, got {type(schedule).__name__}",
                field_name="schedule",
                invalid_value=type(schedule).__name__,
                expected_type="DataFrame",
                error_code="INVALID_SCHEDULE_TYPE"
            )
        
        # Check if schedule is empty
        if schedule.empty:
            enhanced_log(log_callback, f"Warning: Schedule '{schedule_name}' is empty", 'WARNING')
            return True  # Empty is valid, just not useful
        
        # Check for required 'Pier' column
        if 'Pier' not in schedule.columns:
            raise DataValidationError(
                f"Schedule '{schedule_name}' missing required 'Pier' column",
                field_name="schedule_columns",
                invalid_value=list(schedule.columns),
                error_code="MISSING_PIER_COLUMN"
            )
        
        # Check for duplicate pier names
        duplicate_piers = schedule['Pier'].duplicated()
        if duplicate_piers.any():
            duplicates = schedule.loc[duplicate_piers, 'Pier'].tolist()
            raise DataValidationError(
                f"Schedule '{schedule_name}' contains duplicate pier names: {duplicates}",
                field_name="pier_names",
                invalid_value=duplicates,
                error_code="DUPLICATE_PIER_NAMES"
            )
        
        log_calculation_result(log_callback, f"{schedule_name}_rows", len(schedule), "rows")
        log_calculation_result(log_callback, f"{schedule_name}_columns", len(schedule.columns), "columns")
        log_calculation_result(log_callback, f"{schedule_name}_unique_piers", schedule['Pier'].nunique(), "piers")
        
        enhanced_log(log_callback, f"Schedule '{schedule_name}' validation passed", 'DEBUG')
        log_validation_result(log_callback, f"{schedule_name}_validation", True,
                            f"Schedule validated successfully")
        log_function_exit(log_callback, "validate_schedule_data", True)
        
        return True
        
    except Exception as e:
        log_error_with_context(log_callback, e, f"validate_schedule_data({schedule_name})")
        log_validation_result(log_callback, f"{schedule_name}_validation", False, str(e))
        raise


# Export all functions
__all__ = [
    'create_final_schedule',
    'apply_load_factor',
    'apply_load_matrix',
    'merge_schedules',
    'validate_schedule_data'
]

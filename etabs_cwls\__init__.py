"""
ETABS Core Wall Loading Schedule Module - Refactored Professional Edition

This module provides comprehensive tools for managing and processing wall loading schedules
in ETABS (Extended Three-dimensional Analysis of Building Systems). It has been completely
refactored following professional software engineering practices with a modular architecture.

The refactored module features:
- Modular Architecture: Code split into focused, maintainable modules
- Enhanced Error Handling: Comprehensive exception management with custom error types
- Professional Logging: External log file storage with multiple log levels
- Type Safety: Complete type hints throughout the codebase
- Zero Fallback Policy: Explicit error handling with no silent failures
- Comprehensive Documentation: PEP 257 compliant docstrings

Supported Features:
- ETABS 2016/2017 and ETABS 2021 versions
- Pier force and joint reaction analysis
- Load schedule generation and formatting
- GUI-based user interaction
- Excel output generation with professional formatting
- Enhanced logging and error handling

Refactored Architecture:
    console_interface: Console-based user interface (deprecated)
    configuration_manager: ETABS version-specific configuration
    file_path_manager: File path validation and management
    initialization_processor: Design parameter initialization
    pier_force_processor: Pier force data processing
    joint_reaction_processor: Joint reaction data processing
    coordinate_transformer: Mathematical coordinate transformations
    schedule_generator: Load schedule generation and combination

Key Components:
    LoadingScheduleGUI: Main graphical user interface for interactive operations
    file_path: Enhanced file path management with comprehensive validation
    design_data: Professional design data handling with type safety
    ETABS_converter: Version-specific ETABS data conversion with validation

Usage:
    >>> from etabs_cwls import main
    >>> main()  # Launch GUI application

    >>> from etabs_cwls import LoadingScheduleGUI
    >>> import tkinter as tk
    >>> root = tk.Tk()
    >>> app = LoadingScheduleGUI(root)
    >>> root.mainloop()

    # Using the refactored modules directly
    >>> from etabs_cwls.pier_force_processor import create_schedule_pier_force
    >>> from etabs_cwls.configuration_manager import assign_ETABS_converter

Version: 5.6.9 - Refactored Professional Edition
Author: Foundation Automation Team
Copyright: © 2023-2025 Foundation Automation. All rights reserved.
"""

# Import main GUI and core classes (backward compatibility)
from .cwls_gui import LoadingScheduleGUI, main
from ._class import file_path, design_data, ETABS_converter

# Import refactored modules for advanced usage
from . import console_interface
from . import configuration_manager
from . import file_path_manager
from . import initialization_processor
from . import pier_force_processor
from . import joint_reaction_processor
from . import coordinate_transformer
from . import schedule_generator

# Import main functions from refactored modules
from .configuration_manager import assign_ETABS_converter
from .file_path_manager import filepath_selection
from .initialization_processor import initialization
from .pier_force_processor import create_schedule_pier_force
from .joint_reaction_processor import create_schedule_joint_reaction
from .coordinate_transformer import create_raw_schedule, integer_round
from .schedule_generator import create_final_schedule

__version__ = "5.6.9"
__author__ = "Foundation Automation Team"
__description__ = "Refactored Professional Edition"

# Backward compatibility exports
__all__ = [
    # Main GUI and classes
    'LoadingScheduleGUI',
    'main',
    'file_path',
    'design_data',
    'ETABS_converter',

    # Refactored modules
    'console_interface',
    'configuration_manager',
    'file_path_manager',
    'initialization_processor',
    'pier_force_processor',
    'joint_reaction_processor',
    'coordinate_transformer',
    'schedule_generator',

    # Main functions
    'assign_ETABS_converter',
    'filepath_selection',
    'initialization',
    'create_schedule_pier_force',
    'create_schedule_joint_reaction',
    'create_raw_schedule',
    'create_final_schedule',
    'integer_round'
]
